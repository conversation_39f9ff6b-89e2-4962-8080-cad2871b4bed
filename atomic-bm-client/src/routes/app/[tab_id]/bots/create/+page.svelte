<script lang="ts">
	import { run } from 'svelte/legacy';

	import { page } from '$app/stores';
	import { appStore } from '$stores/app-store';
	import * as Card from '$lib/components/ui/card';
	import Input from '$lib/components/ui/input.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import Popover from '$lib/components/ui/popover.svelte';
	import * as Command from '$lib/components/ui/command';
	import Icon from '$lib/components/ui/icon.svelte';
	import { onMount } from 'svelte';
	import {
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		handleErr,
		toast,
		getStorage
	} from '$utils';
	import { browser } from '$app/environment';
	import { postBots } from '$client/bot';
	import { ssp, queryParameters } from 'sveltekit-search-params';

	const queryStore = queryParameters({
		accInfo: ssp.array()
	});

	let { data } = $props();

	let account = $state({
		label: '',
		value: ''
	});
	let symbol = $state('');
	let region = $state('');
	let tag = $state('');

	// Combobox state
	let open = $state(false);

	run(() => {
		if (browser && $page) {
			const accountFromPersist = getPersistData('newbotaccount');
			if (accountFromPersist) {
				account = accountFromPersist;
			} else {
				account = { label: '', value: '' };
			}
			symbol = getPersistData('newbotsymbol');
			region = getPersistData('newbotregion');
			tag = getPersistData('newbottag');
		}
	});

	function clearFieldsData() {
		clearPersistData('newbotaccount');
		clearPersistData('newbotsymbol');
		clearPersistData('newbotregion');
		clearPersistData('newbottag');
	}

	function returnToBots() {
		clearFieldsData();
		tabNavigate('Bots', '/bots');
	}

	let accountOptions = $derived(
		data.accounts?.data.data.map((acc) => {
			if ($queryStore.accInfo) {
				if ($queryStore.accInfo[1] === acc.exchangeId) {
					account = { label: acc.exchangeId, value: acc.id };
				}
			}
			return { label: acc.exchangeId, value: acc.id };
		})
	);

	// Derived value for selected account display
	const selectedAccountLabel = $derived(
		accountOptions?.find((acc) => acc.value === account.value)?.label || ''
	);

	// Function to close combobox
	function closeCombobox() {
		open = false;
	}

	// Function to select an account
	async function selectAccount(selectedAccount: { label: string; value: string }) {
		await persistData(undefined, 'newbotaccount', true, selectedAccount);
		account = selectedAccount;
		closeCombobox();
	}

	async function confirmCreateBot() {
		try {
			await postBots(
				{
					accountId: account.value,
					symbol,
					tag
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);

			clearFieldsData();
			toast('Bot created', 'success');
			tabNavigate('Bots', '/bots');
		} catch (error) {
			handleErr(error);
		}
	}

	function checkNewAccountFromRoute() {
		const accLabel = $page.url.searchParams.get('accLabel');
		const accValue = $page.url.searchParams.get('accValue');
		if (accLabel && accValue) {
			account.label = accLabel;
			account.value = accValue;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'New bot';
		checkNewAccountFromRoute();
	});
</script>

<svelte:head>
	<title>New bot | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmCreateBot}>
		<Card.Header title="New bot" on:iconClick={returnToBots} />
		<Card.Body class="space-y-2">
			<!-- Account Selection Combobox -->
			<div class="flex w-full flex-col gap-1.5">
				<div class="flex flex-col">
					<span class="text-xs opacity-90">
						Account
						<span class="text-xs text-red-900">*</span>
					</span>
					<span class="text-[0.64rem] opacity-70">Every bot must be linked to an account</span>
				</div>

				<Popover
					{open}
					on:openChange={(e) => (open = e.detail)}
					contentClass="w-[--bits-popover-trigger-width] border border-gray-800 bg-dark-9 shadow-lg"
				>
					{#snippet trigger()}
						<Button
							class="bg-dark-7 w-full justify-between border-gray-800 text-xs text-white hover:border-gray-700 focus:border-gray-600"
							role="combobox"
							aria-expanded={open}
						>
							<span class="truncate">
								{selectedAccountLabel || 'Select an account'}
							</span>
							<Icon
								icon="ph:caret-down"
								class="ml-2 h-3 w-3 opacity-50 transition-transform {open ? 'rotate-180' : ''}"
							/>
						</Button>
					{/snippet}

					{#snippet content()}
						<Command.Root class="bg-dark-9 border-0 text-white">
							<Command.Input
								placeholder="Search accounts..."
								class="bg-dark-9 border-b border-gray-800 text-xs text-white placeholder:text-gray-500"
							/>
							<Command.List class="bg-dark-9 max-h-[200px]">
								<Command.Empty class="py-4 text-xs text-gray-200">No account found.</Command.Empty>
								<Command.Group>
									{#each accountOptions || [] as accountOption (accountOption.value)}
										<Command.Item
											value={accountOption.value}
											onSelect={() => selectAccount(accountOption)}
											class="hover:bg-dark-6 aria-selected:bg-dark-6 flex cursor-pointer items-center gap-2 px-3 py-2 text-xs text-white aria-selected:text-white"
										>
											<Icon
												icon="uis:check"
												class={account.value !== accountOption.value
													? 'text-transparent'
													: 'text-blue-1'}
											/>
											<span class="truncate">{accountOption.label}</span>
										</Command.Item>
									{/each}
								</Command.Group>
								<div class="border-t border-gray-800 p-2">
									<Button
										small
										on:click={() => {
											closeCombobox();
											tabNavigate('New account', '/accounts/create?returnToBots=true');
										}}
									>
										<Icon icon="material-symbols:add" class="mr-1" />
										Create new account
									</Button>
								</div>
							</Command.List>
						</Command.Root>
					{/snippet}
				</Popover>
			</div>
			<Input
				id="symbol"
				required
				label="Symbol"
				description="The symbol of the bot"
				placeholder="Insert a Symbol"
				on:input={async (e) => {
					symbol = await persistData(e, 'newbotsymbol');
				}}
				value={symbol}
			/>
			<Input
				id="region"
				required
				label="Region"
				description="The region of the bot"
				placeholder="Insert a Region"
				on:input={async (e) => {
					region = await persistData(e, 'newbotregion');
				}}
				value={region}
			/>
			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the bot"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					tag = await persistData(e, 'newbottag');
				}}
				value={tag}
			/>
		</Card.Body>
		<Card.Footer on:cancel={returnToBots} confirmBtnType="submit" />
	</Card.Main>
</section>
