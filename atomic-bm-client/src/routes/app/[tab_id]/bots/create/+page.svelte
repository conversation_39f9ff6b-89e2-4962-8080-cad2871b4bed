<script lang="ts">
	import { run } from 'svelte/legacy';

	import { page } from '$app/stores';
	import { appStore } from '$stores/app-store';
	import * as Card from '$lib/components/ui/card';
	import Input from '$lib/components/ui/input.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import Popover from '$lib/components/ui/popover.svelte';
	import * as Command from '$lib/components/ui/command';
	import Icon from '$lib/components/ui/icon.svelte';
	import { onMount } from 'svelte';
	import {
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		handleErr,
		toast,
		getStorage
	} from '$utils';
	import { browser } from '$app/environment';
	import { postBots } from '$client/bot';
	import { ssp, queryParameters } from 'sveltekit-search-params';

	const queryStore = queryParameters({
		accInfo: ssp.array()
	});

	let { data } = $props();

	let account = $state({
		label: '',
		value: ''
	});
	let symbol = $state('');
	let region = $state('');
	let tag = $state('');

	// Combobox state
	let open = $state(false);

	run(() => {
		if (browser && $page) {
			const accountFromPersist = getPersistData('newbotaccount');
			if (accountFromPersist) {
				account = accountFromPersist;
			} else {
				account = { label: '', value: '' };
			}
			symbol = getPersistData('newbotsymbol');
			region = getPersistData('newbotregion');
			tag = getPersistData('newbottag');
		}
	});

	function clearFieldsData() {
		clearPersistData('newbotaccount');
		clearPersistData('newbotsymbol');
		clearPersistData('newbotregion');
		clearPersistData('newbottag');
	}

	function returnToBots() {
		clearFieldsData();
		tabNavigate('Bots', '/bots');
	}

	let accountOptions = $derived(
		data.accounts?.data.data.map((acc) => {
			if ($queryStore.accInfo) {
				if ($queryStore.accInfo[1] === acc.exchangeId) {
					account = { label: acc.exchangeId, value: acc.id };
				}
			}
			return { label: acc.exchangeId, value: acc.id };
		})
	);

	// Derived value for selected account display
	const selectedAccountLabel = $derived(
		accountOptions?.find((acc) => acc.value === account.value)?.label || ''
	);

	// Function to close combobox
	function closeCombobox() {
		open = false;
	}

	// Function to select an account
	async function selectAccount(selectedAccount: { label: string; value: string }) {
		await persistData(undefined, 'newbotaccount', true, selectedAccount);
		account = selectedAccount;
		closeCombobox();
	}

	async function confirmCreateBot() {
		try {
			await postBots(
				{
					accountId: account.value,
					symbol,
					tag
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);

			clearFieldsData();
			toast('Bot created', 'success');
			tabNavigate('Bots', '/bots');
		} catch (error) {
			handleErr(error);
		}
	}

	function checkNewAccountFromRoute() {
		const accLabel = $page.url.searchParams.get('accLabel');
		const accValue = $page.url.searchParams.get('accValue');
		if (accLabel && accValue) {
			account.label = accLabel;
			account.value = accValue;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'New bot';
		checkNewAccountFromRoute();
	});
</script>

<svelte:head>
	<title>New bot | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmCreateBot}>
		<Card.Header title="New bot" on:iconClick={returnToBots} />
		<Card.Body class="space-y-2">
			<!-- Account Selection Combobox -->
			<div class="flex w-full flex-col gap-1.5">
				<div class="flex flex-col">
					<span class="text-xs opacity-90">
						Account
						<span class="text-xs text-red-900">*</span>
					</span>
					<span class="text-[0.64rem] opacity-70">Every bot must be linked to an account</span>
				</div>

				<Popover {open} on:openChange={(e) => (open = e.detail)}>
					{#snippet trigger()}
						<Button
							variant="outline"
							class="w-full justify-between text-xs"
							role="combobox"
							aria-expanded={open}
						>
							{selectedAccountLabel || 'Select an account'}
							<Icon icon="ph:caret-down" class="ml-2 opacity-50" />
						</Button>
					{/snippet}

					{#snippet content()}
						<div class="w-full p-0">
							<Command.Root>
								<Command.Input placeholder="Search accounts..." class="text-xs" />
								<Command.List>
									<Command.Empty>No account found.</Command.Empty>
									<Command.Group>
										{#each accountOptions || [] as accountOption (accountOption.value)}
											<Command.Item
												value={accountOption.value}
												onSelect={() => selectAccount(accountOption)}
												class="text-xs"
											>
												<Icon
													icon="uis:check"
													class="mr-2 h-4 w-4 {account.value !== accountOption.value
														? 'text-transparent'
														: ''}"
												/>
												{accountOption.label}
											</Command.Item>
										{/each}
									</Command.Group>
									<div class="border-t p-2">
										<Button
											size="sm"
											class="w-full text-xs"
											on:click={() => {
												closeCombobox();
												tabNavigate('New account', '/accounts/create?returnToBots=true');
											}}
										>
											Create new account
										</Button>
									</div>
								</Command.List>
							</Command.Root>
						</div>
					{/snippet}
				</Popover>
			</div>
			<Input
				id="symbol"
				required
				label="Symbol"
				description="The symbol of the bot"
				placeholder="Insert a Symbol"
				on:input={async (e) => {
					symbol = await persistData(e, 'newbotsymbol');
				}}
				value={symbol}
			/>
			<Input
				id="region"
				required
				label="Region"
				description="The region of the bot"
				placeholder="Insert a Region"
				on:input={async (e) => {
					region = await persistData(e, 'newbotregion');
				}}
				value={region}
			/>
			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the bot"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					tag = await persistData(e, 'newbottag');
				}}
				value={tag}
			/>
		</Card.Body>
		<Card.Footer on:cancel={returnToBots} confirmBtnType="submit" />
	</Card.Main>
</section>
