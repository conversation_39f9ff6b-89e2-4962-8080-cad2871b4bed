<script lang="ts">
	import { flip } from 'svelte/animate';
	import type { Tab } from '$types';
	import { createEventDispatcher } from 'svelte';
	import { dndzone, type DndEvent } from 'svelte-dnd-action';
	import { updateOrderNumbers } from '$utils';
	import BaseLoading from './base-loading.svelte';
	import Card from './ui/card/card.svelte';
	import Icon from './ui/icon.svelte';

	const d = createEventDispatcher();
	interface Props {
		flipDurationMs?: number;
		tabs?: Tab[];
		lastTabID?: string;
		loading?: boolean;
	}

	let {
		flipDurationMs = 100,
		tabs = $bindable([]),
		lastTabID = '',
		loading = true
	}: Props = $props();

	function handleConsider(evt: CustomEvent<DndEvent<Tab>>) {
		tabs = evt.detail.items;
	}

	function handleFinalize(evt: CustomEvent<DndEvent<Tab>>) {
		if (!evt.detail.items) return;
		tabs = evt.detail.items;
		const reorderedTabs = updateOrderNumbers(tabs).map((t: Tab) => {
			return {
				id: t.id,
				order: t.order
			};
		});
		d('reorderTabs', reorderedTabs);
	}
</script>

{#if loading}
	<div class="flex w-full items-center justify-center">
		<BaseLoading iconClass="text-base" />
	</div>
{:else}
	<div
		class="flex w-[99%] divide-x divide-gray-800 overflow-x-auto overflow-y-hidden max-[980px]:hidden"
		use:dndzone={{
			items: tabs,
			dropTargetStyle: {
				outline: 'none'
			},
			flipDurationMs
		}}
		onconsider={handleConsider}
		onfinalize={handleFinalize}
	>
		{#each tabs as tab (tab.id)}
			<div animate:flip={{ duration: flipDurationMs }}>
				<Card
					freeSize
					class={`rounded-xs flex h-[20px] max-w-[200px] items-center border-none text-xs font-semibold ${lastTabID !== tab.id && 'opacity-50'} hover:!opacity-80`}
				>
					<button
						class="flex-1 py-1 pl-2 pr-4"
						class:pl-0={tabs.length <= 1}
						onclick={() => d('tabClick', tab.id)}
					>
						<h1>{tab.name} oi</h1>
					</button>
					{#if tabs.length > 1}
						<Icon
							icon="material-symbols:close"
							class="!cursor-pointer py-1 pr-1.5 hover:opacity-40"
							on:click={() => d('tabClose', tab.id)}
						/>
					{/if}
				</Card>
			</div>
		{:else}
			<div class="w-full flex justify-center items-center pt-0.5">
				<Icon icon="eos-icons:three-dots-loading" />
			</div>
		{/each}
		<div class="flex items-center pl-1">
			<Icon icon="material-symbols:add" btn on:click={() => d('tabCreate')} />
		</div>
	</div>
	<div
		class="hidden w-[99%] divide-x divide-gray-800 overflow-x-auto overflow-y-hidden max-[980px]:flex"
	>
		{#each tabs as tab (tab.id)}
			<div>
				<Card
					freeSize
					class={`rounded-xs flex h-[20px] max-w-[200px] items-center border-none text-xs font-semibold ${lastTabID !== tab.id && 'opacity-50'} hover:!opacity-80`}
				>
					<button
						class="flex-1 py-1 pl-2 pr-4"
						class:pl-0={tabs.length <= 1}
						onclick={() => d('tabClick', tab.id)}
					>
						<h1>{tab.name}</h1>
					</button>
					{#if tabs.length > 1}
						<Icon
							icon="material-symbols:close"
							class="!cursor-pointer py-1 pr-1.5 hover:opacity-40"
							on:click={() => d('tabClose', tab.id)}
						/>
					{/if}
				</Card>
			</div>
		{:else}
			<div class="w-full flex justify-center items-center pt-0.5">
				<Icon icon="eos-icons:three-dots-loading" />
			</div>
		{/each}
		<div class="flex items-center pl-1">
			<Icon icon="material-symbols:add" btn on:click={() => d('tabCreate')} />
		</div>
	</div>
{/if}
