<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import { createEventDispatcher } from 'svelte';
	import Button from '../button.svelte';

	const d = createEventDispatcher();
	export let empty = false,
		cancelText = 'Cancelar',
		confirmText = 'Confirmar',
		cancelClass = '',
		confirmBtnType: 'button' | 'submit' | 'reset' = 'button',
		confirmClass = '';
</script>

{#if empty}
	<div class={cn('mt-3 p-2', $$props.class)}>
		<slot />
	</div>
{:else}
	<div class={cn('mt-3 flex h-[50px] items-center justify-end gap-2 p-3', $$props.class)}>
		<Button class={cn('', cancelClass)} on:click={() => d('cancel')}>{cancelText}</Button>
		<Button type={confirmBtnType} class={cn('', confirmClass)} on:click={() => d('confirm')}
			>{confirmText}</Button
		>
	</div>
{/if}
