/**
 * Generated by orval v6.29.1 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	CreateParamsRequest,
	DeleteParamsRequest,
	Error,
	GetParamsParams,
	PageResponseArrayParam,
	ResponseArrayParam
} from './api.schemas';

/**
 * List parameters with optional filtering and ordering based on scope or scopeId.
 * @summary List parameters.
 */
export const getParams = (
	params?: GetParamsParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayParam>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/params`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetParamsQueryKey = (params?: GetParamsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/params`, ...(params ? [params] : [])] as const;
};

export const getGetParamsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParams>>,
	TError = AxiosError<Error>
>(
	params?: GetParamsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getParams>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParams>>> = () =>
		getParams(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParams>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsQueryResult = NonNullable<Awaited<ReturnType<typeof getParams>>>;
export type GetParamsQueryError = AxiosError<Error>;

/**
 * @summary List parameters.
 */
export const createGetParams = <
	TData = Awaited<ReturnType<typeof getParams>>,
	TError = AxiosError<Error>
>(
	params?: GetParamsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getParams>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates parameters for one or more scopes (gateway, account, bot, etc.) in a single request.
When the Scope is 'gateway' or 'account', the scopeId must be a valid accountId.
When the Scope is 'bot', the scopeId must be a valid botId.
When the Scope is 'param_group', the scopeId must be a valid paramGroupId.
 * @summary Create parameters.
 */
export const postParams = (
	createParamsRequest: CreateParamsRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayParam>> => {
	return axios.post(`${import.meta.env.VITE_ATOMIC_BASEURL}/params`, createParamsRequest, options);
};

/**
 * Delete parameters by their keys and scope with the specified scopeID.
 * @summary Delete parameters.
 */
export const postParamsBulkDelete = (
	deleteParamsRequest: DeleteParamsRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/params/bulkDelete`,
		deleteParamsRequest,
		options
	);
};

/**
 * Gets the active parameters based on botId.
 * @summary Get active parameters.
 */
export const getParamsBotIdActiveParams = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayParam>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/params/${botId}/activeParams`, options);
};

const getGetParamsBotIdActiveParamsQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/params/${botId}/activeParams`] as const;
};

export const getGetParamsBotIdActiveParamsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsBotIdActiveParamsQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>> = () =>
		getParamsBotIdActiveParams(botId, axiosOptions);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsBotIdActiveParamsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getParamsBotIdActiveParams>>
>;
export type GetParamsBotIdActiveParamsQueryError = AxiosError<Error>;

/**
 * @summary Get active parameters.
 */
export const createGetParamsBotIdActiveParams = <
	TData = Awaited<ReturnType<typeof getParamsBotIdActiveParams>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsBotIdActiveParams>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsBotIdActiveParamsQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
