/**
 * Generated by orval v6.29.1 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	CreateBotRequest,
	Error,
	GetBotsParams,
	PageResponseArrayBot,
	ResponseArrayListBotsStateResponse,
	ResponseBot,
	ResponseGetBotStateResponse,
	UpdateBotRequest
} from './api.schemas';

/**
 * Retrieves a list of bots with optional filtering and pagination parameters.
 * @summary Lists bots with custom filters.
 */
export const getBots = (
	params?: GetBotsParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayBot>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetBotsQueryKey = (params?: GetBotsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots`, ...(params ? [params] : [])] as const;
};

export const getGetBotsQueryOptions = <
	TData = Awaited<ReturnType<typeof getBots>>,
	TError = AxiosError<Error>
>(
	params?: GetBotsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBots>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBots>>> = () =>
		getBots(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBots>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsQueryResult = NonNullable<Awaited<ReturnType<typeof getBots>>>;
export type GetBotsQueryError = AxiosError<Error>;

/**
 * @summary Lists bots with custom filters.
 */
export const createGetBots = <
	TData = Awaited<ReturnType<typeof getBots>>,
	TError = AxiosError<Error>
>(
	params?: GetBotsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBots>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates a new bot with the specified parameters.
 * @summary Create a new bot.
 */
export const postBots = (
	createBotRequest: CreateBotRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseBot>> => {
	return axios.post(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots`, createBotRequest, options);
};

/**
 * Retrieves a cached state of all bots. The data is populated by a background worker.
 * @summary Get bots state.
 */
export const getBotsState = (
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayListBotsStateResponse>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/state`, options);
};

const getGetBotsStateQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/state`] as const;
};

export const getGetBotsStateQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsState>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsState>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsStateQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsState>>> = () =>
		getBotsState(axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsState>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsStateQueryResult = NonNullable<Awaited<ReturnType<typeof getBotsState>>>;
export type GetBotsStateQueryError = AxiosError<Error>;

/**
 * @summary Get bots state.
 */
export const createGetBotsState = <
	TData = Awaited<ReturnType<typeof getBotsState>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsState>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsStateQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Retrieve a bot with the specified ID.
 * @summary Retrieve a bot by ID.
 */
export const getBotsBotId = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseBot>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`, options);
};

const getGetBotsBotIdQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`] as const;
};

export const getGetBotsBotIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsBotId>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotId>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsBotIdQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsBotId>>> = () =>
		getBotsBotId(botId, axiosOptions);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsBotId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsBotIdQueryResult = NonNullable<Awaited<ReturnType<typeof getBotsBotId>>>;
export type GetBotsBotIdQueryError = AxiosError<Error>;

/**
 * @summary Retrieve a bot by ID.
 */
export const createGetBotsBotId = <
	TData = Awaited<ReturnType<typeof getBotsBotId>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotId>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsBotIdQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Delete a bot with the specified ID.
 * @summary Delete a bot by ID.
 */
export const deleteBotsBotId = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.delete(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`, options);
};

/**
 * Update an existing bot with the specified ID by providing account ID, symbol, region, or status.
You need to ensure that at least one field is provided in the update request.
 * @summary Update an existing bot by ID.
 */
export const patchBotsBotId = (
	botId: string,
	updateBotRequest: UpdateBotRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseBot>> => {
	return axios.patch(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}`,
		updateBotRequest,
		options
	);
};

/**
 * It creates a bot based on the botId.
 * @summary StartBot create a new bot if not exists.
 */
export const postBotsBotIdStart = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/start`,
		undefined,
		options
	);
};

/**
 * Retrieves the current state of a specific bot from the botregional service. It uses a short-lived cache to reduce load.
 * @summary Get bot state.
 */
export const getBotsBotIdState = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseGetBotStateResponse>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/state`, options);
};

const getGetBotsBotIdStateQueryKey = (botId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/state`] as const;
};

export const getGetBotsBotIdStateQueryOptions = <
	TData = Awaited<ReturnType<typeof getBotsBotIdState>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotIdState>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetBotsBotIdStateQueryKey(botId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotsBotIdState>>> = () =>
		getBotsBotIdState(botId, axiosOptions);

	return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getBotsBotIdState>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetBotsBotIdStateQueryResult = NonNullable<
	Awaited<ReturnType<typeof getBotsBotIdState>>
>;
export type GetBotsBotIdStateQueryError = AxiosError<Error>;

/**
 * @summary Get bot state.
 */
export const createGetBotsBotIdState = <
	TData = Awaited<ReturnType<typeof getBotsBotIdState>>,
	TError = AxiosError<Error>
>(
	botId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getBotsBotIdState>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetBotsBotIdStateQueryOptions(botId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * It stops a bot based on the botId.
 * @summary StopBot stop a bot if exists.
 */
export const postBotsBotIdStop = (
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/bots/${botId}/stop`,
		undefined,
		options
	);
};
