# 🚦 Atomic Funds Changelog

## [1.65.0](https://github.com/herenow/atomic-gtw/compare/v1.64.0...v1.65.0) (2025-07-27)


### ✨ Features

* **pancakeswapv3:** add market data integration ([7bd292e](https://github.com/herenow/atomic-gtw/commit/7bd292e9ed6bde94262c746a2afce369d691b5c9))
* **uniswapv2:** add basic structure for handling dex ([c1b57f7](https://github.com/herenow/atomic-gtw/commit/c1b57f7b030807a2adce30849c2a308e748e86e7))
* **uniswapv2:** fix unit tests and integration tests ([5ec5dae](https://github.com/herenow/atomic-gtw/commit/5ec5daeef1069708137c69070588254188222954))
* **uniswapv2:** remove DEXProvider interface ([0c8fb1a](https://github.com/herenow/atomic-gtw/commit/0c8fb1a9d48545a3e37019bb36545fd9e88c9412))
* **uniswapv2:** rename integration plus add stream events via wss ([0f75e63](https://github.com/herenow/atomic-gtw/commit/0f75e634bb5c83da175a37f52bb299706b74a6de))
* **uniswapv3:** added uniswapv3 integration ([6b67f76](https://github.com/herenow/atomic-gtw/commit/6b67f76ad599784cdb487f3a0fcb9d7b78c05039))
* **uniswapv4:** added v4 integration ([0efb83a](https://github.com/herenow/atomic-gtw/commit/0efb83a35b9cd5123705e68109d8b28cddf86091))


### 📦 Chores

* **dex:** adjust the way we fetch ticks and orderbook ([5e7ac7d](https://github.com/herenow/atomic-gtw/commit/5e7ac7db549ff518bb180e91995aff72a3699255))
* **uniswap:** restructured uniswap integrations ([5458a9a](https://github.com/herenow/atomic-gtw/commit/5458a9a4c140284a041293bb8175f96d2f90e9d2))
* **uniswapv3:** added complex logic ([5ffa248](https://github.com/herenow/atomic-gtw/commit/5ffa24872d88acb72e818148622c5498b49cb1d4))
* **uniswapv3:** improve code quality ([9fb8947](https://github.com/herenow/atomic-gtw/commit/9fb89471e744204908942d2b35d4ed76ffb98023))
* **uniswapv3:** merge generate_book code into api.go ([36889f7](https://github.com/herenow/atomic-gtw/commit/36889f7b71cc792aa7bf3c69d68babb5d62f07d6))
* **uniswapv3:** merge tick provider code into tick lens ([f760ed6](https://github.com/herenow/atomic-gtw/commit/f760ed6a72084d4d409f383c055172cede79b3ee))
* **uniswapv3:** move univ3 package to main package ([911a6c0](https://github.com/herenow/atomic-gtw/commit/911a6c0dda5a9cde3ee7f74c08d9e9caf38beec4))
* **uniswapv3:** remove unused code ([1232ea9](https://github.com/herenow/atomic-gtw/commit/1232ea9e01d2869b0a5d422d9c77519d224882ba))
* **uniswapv3:** rename variable on nextInitTick logic ([5276dac](https://github.com/herenow/atomic-gtw/commit/5276dac38230a969e101622266093a6a34a3538d))
* **uniswapv3:** replace polling with web sockets ([b3c40f0](https://github.com/herenow/atomic-gtw/commit/b3c40f00366b2557facf948e727ba2e15909c2d6))
* **uniswapv4:** adjust GetPool to support native eth token ([ec24dc1](https://github.com/herenow/atomic-gtw/commit/ec24dc1fa63f4aab97d2f6b518f2c2b5227cb62a))
* **uniswapv4:** infer tick spacing from the pool fee ([2bad165](https://github.com/herenow/atomic-gtw/commit/2bad1655636554f714d9bdbc1fd974ae0a7fd425))
* **univ4:** adjust the way we fetch ticks and orderbook ([c9b3852](https://github.com/herenow/atomic-gtw/commit/c9b38526b7b12124b9263d2832fd0e3e0bf58ddc))
* **univ4:** adjust the way we fetch ticks and orderbook ([95c29e9](https://github.com/herenow/atomic-gtw/commit/95c29e9972a76c575eea60734b0bfe02f8c79a3f))

## [1.64.0](https://github.com/herenow/atomic-gtw/compare/v1.63.3...v1.64.0) (2025-07-25)


### ✨ Features

* **book:** Remove book and use book from atomic-tools ([b6e0aec](https://github.com/herenow/atomic-gtw/commit/b6e0aec52eebf62e863f9b03e181724f90994dd5))

## [1.63.3](https://github.com/herenow/atomic-gtw/compare/v1.63.2...v1.63.3) (2025-07-25)


### 🐛 Bug Fixes

* **foxbit:** Use price/amount increments for ticks ([7997bdd](https://github.com/herenow/atomic-gtw/commit/7997bdd7de09fc874e123ab81f8dbf5d3a0bf7b2))

## [1.63.2](https://github.com/herenow/atomic-gtw/compare/v1.63.1...v1.63.2) (2025-07-25)


### 📦 Chores

* **foxbit:** Use gateway.PriceArray for depth book ([0cd5797](https://github.com/herenow/atomic-gtw/commit/0cd5797bef157f50898b15f91b8cbdca35aad7f3))


### 🐛 Bug Fixes

* **foxbit:** Increase default price tick ([ed3721a](https://github.com/herenow/atomic-gtw/commit/ed3721a921988ae63aaf479fdf09823bb3f124ec))

## [1.63.1](https://github.com/herenow/atomic-gtw/compare/v1.63.0...v1.63.1) (2025-07-24)


### 🐛 Bug Fixes

* **kraken:** Ws messages was being processed twice ([314070b](https://github.com/herenow/atomic-gtw/commit/314070b8946421be3f14c58623459eb9c6ab6274))

## [1.63.0](https://github.com/herenow/atomic-gtw/compare/v1.62.12...v1.63.0) (2025-07-24)


### ✨ Features

* **kraken:** Process trade timestamp ([83606f6](https://github.com/herenow/atomic-gtw/commit/83606f632589fd4e954a965bcf8c6759f0c3ceb4))

## [1.62.12](https://github.com/herenow/atomic-gtw/compare/v1.62.11...v1.62.12) (2025-07-22)


### 📦 Chores

* **bitmart:** Set stop mode when post only order ([3c437ae](https://github.com/herenow/atomic-gtw/commit/3c437ae9d8e4a69048597ab8105ed2ff43c8c66f))

## [1.62.11](https://github.com/herenow/atomic-gtw/compare/v1.62.10...v1.62.11) (2025-07-21)


### 📦 Chores

* **readme:** Update list of integrations ([f2608de](https://github.com/herenow/atomic-gtw/commit/f2608de4a4e979fd7d3ae8dc3ddac1b316a9b9ea))

## [1.62.10](https://github.com/herenow/atomic-gtw/compare/v1.62.9...v1.62.10) (2025-07-21)


### 📦 Chores

* **binance:** Skip empty order updates ([cdd24b6](https://github.com/herenow/atomic-gtw/commit/cdd24b6e72d7d9aa0d0c5a02e163df740f5ac957))

## [1.62.9](https://github.com/herenow/atomic-gtw/compare/v1.62.8...v1.62.9) (2025-07-18)


### 📦 Chores

* **kraken:** Map rate limit err ([d105f49](https://github.com/herenow/atomic-gtw/commit/d105f495d832f237014da63bdf6550e5908aef4d))

## [1.62.8](https://github.com/herenow/atomic-gtw/compare/v1.62.7...v1.62.8) (2025-07-18)


### 🐛 Bug Fixes

* **kraken:** Aggregate balances ([d8aaa40](https://github.com/herenow/atomic-gtw/commit/d8aaa4079bc50fad7dc160c94eda2de139f9e62b))

## [1.62.7](https://github.com/herenow/atomic-gtw/compare/v1.62.6...v1.62.7) (2025-07-18)


### 🐛 Bug Fixes

* **kraken:** Trim white spaces from asset name ([b730adc](https://github.com/herenow/atomic-gtw/commit/b730adc517a9383a391407573a5ba13c2181015e))

## [1.62.6](https://github.com/herenow/atomic-gtw/compare/v1.62.5...v1.62.6) (2025-07-18)


### 🐛 Bug Fixes

* **kraken:** Use the extended balance API ([60c2a82](https://github.com/herenow/atomic-gtw/commit/60c2a824b187966dcf8d78361383d6374fc9b09e))

## [1.62.5](https://github.com/herenow/atomic-gtw/compare/v1.62.4...v1.62.5) (2025-07-18)


### 🐛 Bug Fixes

* **binance:** Update ws account gateway staging url ([9818a95](https://github.com/herenow/atomic-gtw/commit/9818a9559e5948fcab123338f828174add225d0f))
* **kraken:** Normalize balance asset name ([cd85060](https://github.com/herenow/atomic-gtw/commit/cd85060b8ffb59fdd1f10da67df925d2f94fe42b))

## [1.62.4](https://github.com/herenow/atomic-gtw/compare/v1.62.3...v1.62.4) (2025-07-18)


### 🐛 Bug Fixes

* **kraken:** Concurrency when generating nonce ([6189f30](https://github.com/herenow/atomic-gtw/commit/6189f307efd2d65f5c505de531a2fdd6c165f8e8))

## [1.62.3](https://github.com/herenow/atomic-gtw/compare/v1.62.2...v1.62.3) (2025-07-17)


### 📦 Chores

* **kraken:** Various small fixes of warnings ([de6812a](https://github.com/herenow/atomic-gtw/commit/de6812a3e01b36d8f38fca63be073351328bd7f3))

## [1.62.2](https://github.com/herenow/atomic-gtw/compare/v1.62.1...v1.62.2) (2025-07-17)


### 🐛 Bug Fixes

* **kraken:** GetDepthBook was not working ([6c56781](https://github.com/herenow/atomic-gtw/commit/6c567813fe0ef19295ecb8222a9aebf1bf459f92))

## [1.62.1](https://github.com/herenow/atomic-gtw/compare/v1.62.0...v1.62.1) (2025-07-14)


### 📦 Chores

* **gateway:** Order states update ([a58b2b0](https://github.com/herenow/atomic-gtw/commit/a58b2b010bb3dd00908ed753bd145619e926c9fc))

## [1.62.0](https://github.com/herenow/atomic-gtw/compare/v1.61.4...v1.62.0) (2025-07-03)


### ✨ Features

* **gateway:** Market type identification ([c81b09a](https://github.com/herenow/atomic-gtw/commit/c81b09ab5c4c9b82327294d8d86b8f3e140c83b6))

## [1.61.4](https://github.com/herenow/atomic-gtw/compare/v1.61.3...v1.61.4) (2025-06-24)


### 🐛 Bug Fixes

* **biconomy:** Update mktd endpoint url ([0453b47](https://github.com/herenow/atomic-gtw/commit/0453b47c620ad3167d17627e27bdb6861c0b91d5))

## [1.61.3](https://github.com/herenow/atomic-gtw/compare/v1.61.2...v1.61.3) (2025-06-15)


### 📦 Chores

* **cryptocom:** Map orders not opened because of post only ([9749e88](https://github.com/herenow/atomic-gtw/commit/9749e88ca9f602c4a31d138778cbf34ca1ce9f8f))

## [1.61.2](https://github.com/herenow/atomic-gtw/compare/v1.61.1...v1.61.2) (2025-06-11)


### 🐛 Bug Fixes

* **hyperliquid:** Each update is a Snapshot sequence ([c5d31a7](https://github.com/herenow/atomic-gtw/commit/c5d31a71827f304e994bf8eaff54f8829dee9109))

## [1.61.1](https://github.com/herenow/atomic-gtw/compare/v1.61.0...v1.61.1) (2025-06-11)


### 🐛 Bug Fixes

* **hyperliquid:** Market metadata to work with safegateway ([097502b](https://github.com/herenow/atomic-gtw/commit/097502b39893d0a1b308c3d56abbac3a07a26b73))

## [1.61.0](https://github.com/herenow/atomic-gtw/compare/v1.60.1...v1.61.0) (2025-06-11)


### ✨ Features

* **hyperliquid:** Support spot markets ([0feba6a](https://github.com/herenow/atomic-gtw/commit/0feba6a91560363825312bbbaaa18f9608bfc568))

## [1.60.1](https://github.com/herenow/atomic-gtw/compare/v1.60.0...v1.60.1) (2025-06-02)


### 🐛 Bug Fixes

* **cryptocom:** Missing account base url for ws ([4f46977](https://github.com/herenow/atomic-gtw/commit/4f46977bc9bfeee11e5c252f6f7b5a96c7566a31))

## [1.60.0](https://github.com/herenow/atomic-gtw/compare/v1.59.5...v1.60.0) (2025-06-02)


### ✨ Features

* **cryptocom:** Allow for custom api/ws base url to be set ([ba4d509](https://github.com/herenow/atomic-gtw/commit/ba4d5091fad2042be1258182259168e9e76d5be1))

## [1.59.5](https://github.com/herenow/atomic-gtw/compare/v1.59.4...v1.59.5) (2025-06-01)


### 🐛 Bug Fixes

* **valr:** Fetch open orders from correct endpoint ([591d11a](https://github.com/herenow/atomic-gtw/commit/591d11abd6421629c9672688dee3a7d63683e1b4))

## [1.59.4](https://github.com/herenow/atomic-gtw/compare/v1.59.3...v1.59.4) (2025-05-31)


### 🐛 Bug Fixes

* **book:** Add missing book pkg ([c44c85b](https://github.com/herenow/atomic-gtw/commit/c44c85ba6c11b9a9b414172a0acf8a2bc4aea267))

## [1.59.3](https://github.com/herenow/atomic-gtw/compare/v1.59.2...v1.59.3) (2025-05-31)


### 🐛 Bug Fixes

* **runtime:** Move back to go 1.22 ([5ab716d](https://github.com/herenow/atomic-gtw/commit/5ab716d79e9d311ed3e34741bd41c5f7f3aca37b))

## [1.59.2](https://github.com/herenow/atomic-gtw/compare/v1.59.1...v1.59.2) (2025-05-31)


### 🕐 Reverts

* Revert "chore(http_client): Move to fasthttp" ([92032a7](https://github.com/herenow/atomic-gtw/commit/92032a744f49f5b2f1a923037f420f044e765a05))

## [1.59.1](https://github.com/herenow/atomic-gtw/compare/v1.59.0...v1.59.1) (2025-05-31)


### 📦 Chores

* **http_client:** Move to fasthttp ([91d91d6](https://github.com/herenow/atomic-gtw/commit/91d91d67680ba07c5bfeef081bf9154d22f57ff8))

## [1.59.0](https://github.com/herenow/atomic-gtw/compare/v1.58.0...v1.59.0) (2025-05-31)


### ✨ Features

* **temp_uniswapv4_evawbtc:** Temporary integration for EVA/WBTC prices ([7a7ae41](https://github.com/herenow/atomic-gtw/commit/7a7ae41f226b0910fc84a26d5b4f31052a16b96d))

## [1.58.0](https://github.com/herenow/atomic-gtw/compare/v1.57.0...v1.58.0) (2025-05-31)


### ✨ Features

* gateio depthbook ([3d26ac1](https://github.com/herenow/atomic-gtw/commit/****************************************))


### 🐛 Bug Fixes

* **gateio:** add timeout to HTTP requests and update ioutil to io package ([33b0426](https://github.com/herenow/atomic-gtw/commit/33b042638039ba4323fdc793f17e7b8b4eacc185))


### ♻️ Refactor

* **gateway:** update depth book structure and simplify price level parsing ([acff16c](https://github.com/herenow/atomic-gtw/commit/acff16c50cf24a03b90fdf648891b54437280e36))

## [1.57.0](https://github.com/herenow/atomic-gtw/compare/v1.56.0...v1.57.0) (2025-05-31)


### ✨ Features

* bitstamp depth book ([1cebbe7](https://github.com/herenow/atomic-gtw/commit/1cebbe7c3e95cd1e4d9a1dd0bb6d8b6a2ce35349))


### ♻️ Refactor

* **bitstamp:** simplify order book API and update ioutil to io ([6fc92b5](https://github.com/herenow/atomic-gtw/commit/6fc92b58dc11664a3da994d64dd57289e9657089))


### 🐛 Bug Fixes

* **bitstamp:** correct URL path formatting in GetOrderBook function ([0810770](https://github.com/herenow/atomic-gtw/commit/0810770c054493bd51d16836ddf5735047d7237c))

## [1.56.0](https://github.com/herenow/atomic-gtw/compare/v1.55.1...v1.56.0) (2025-05-29)


### ✨ Features

* **backpack:** new integration with all functionalities ([d2ce08b](https://github.com/herenow/atomic-gtw/commit/d2ce08be845b1d92218c53e0b7bdc462e349a3d9))


### 🐛 Bug Fixes

* **backpack:** adjust signature ([6eff0cc](https://github.com/herenow/atomic-gtw/commit/6eff0ccaa4cd58b6eea98a06bcdba1fc6994c6fe))


### 📦 Chores

* **backpack:** request changes ([f38c927](https://github.com/herenow/atomic-gtw/commit/f38c9273a025e48a939e78eaf5ae25d4d96bd83b))

## [1.55.1](https://github.com/herenow/atomic-gtw/compare/v1.55.0...v1.55.1) (2025-05-29)


### 🐛 Bug Fixes

* **binance:** OpenOrders incorrect slice initialization ([8a414fc](https://github.com/herenow/atomic-gtw/commit/8a414fcca9a31daa6cdec5068e128391a55546db))

## [1.55.0](https://github.com/herenow/atomic-gtw/compare/v1.54.9...v1.55.0) (2025-05-29)


### ✨ Features

* **binance:** Send gateway.Fill instead of gateway.OrderUpdate ([be31bd8](https://github.com/herenow/atomic-gtw/commit/be31bd8fb320ad390b4d3c9e5f5c71f8f6b09ad0))

## [1.54.9](https://github.com/herenow/atomic-gtw/compare/v1.54.8...v1.54.9) (2025-05-29)


### 🐛 Bug Fixes

* some unit tests ([53c4d5e](https://github.com/herenow/atomic-gtw/commit/53c4d5edfee6fa0468391cf7966c766dcc53f3f6))

## [1.54.8](https://github.com/herenow/atomic-gtw/compare/v1.54.7...v1.54.8) (2025-05-27)


### 🐛 Bug Fixes

* **gateway:** Fix memoization on Exchange value type ([737728d](https://github.com/herenow/atomic-gtw/commit/737728d1784a17f208f9ac7536e7ca9634e2b029))

## [1.54.7](https://github.com/herenow/atomic-gtw/compare/v1.54.6...v1.54.7) (2025-05-26)


### 🐛 Bug Fixes

* **hyperliquid:** Fix ping WriteMessage ([e01cee1](https://github.com/herenow/atomic-gtw/commit/e01cee1c44543da09e4cab0864fc8003cc08ced2))

## [1.54.6](https://github.com/herenow/atomic-gtw/compare/v1.54.5...v1.54.6) (2025-05-26)


### 🐛 Bug Fixes

* **hyperliquid:** remove pong logic ([4ef5eda](https://github.com/herenow/atomic-gtw/commit/4ef5edac3e46e026644fbd99911958436b7c9e75))

## [1.54.5](https://github.com/herenow/atomic-gtw/compare/v1.54.4...v1.54.5) (2025-05-26)


### 📦 Chores

* **pkg:** Use atomic-tools book pkg ([9ba3aac](https://github.com/herenow/atomic-gtw/commit/9ba3aac4bee51732c3ae69bb87aef2f5d91b0067))

## [1.54.4](https://github.com/herenow/atomic-gtw/compare/v1.54.3...v1.54.4) (2025-05-23)


### 🐛 Bug Fixes

* **okx:** Invalid float64 on symbols response ([7e6cde1](https://github.com/herenow/atomic-gtw/commit/7e6cde10942ce426bbec3dd75bae4588eea8eb94))

## [1.54.3](https://github.com/herenow/atomic-gtw/compare/v1.54.2...v1.54.3) (2025-05-22)


### 📦 Chores

* **coinsph,hyperliquid,novadax:** Remove fmt used as log print ([0805335](https://github.com/herenow/atomic-gtw/commit/0805335b0b481c770a45f764549e7e2dc8f2b302))

## [1.54.2](https://github.com/herenow/atomic-gtw/compare/v1.54.1...v1.54.2) (2025-05-21)


### 🐛 Bug Fixes

* **valr:** add order types API endpoint and use to filter markets with LIMIT order type ([322d86b](https://github.com/herenow/atomic-gtw/commit/322d86b20cb3aff28066e0cb6119ff13e0824364))
* **valr:** update order types API to include inactive pairs and remove inactive symbol filtering ([c95a863](https://github.com/herenow/atomic-gtw/commit/c95a8632e65d2e75f6a79deea0b78cbea4f2976c))

## [1.54.1](https://github.com/herenow/atomic-gtw/compare/v1.54.0...v1.54.1) (2025-05-20)


### 🐛 Bug Fixes

* **hyperliquid:** Subscribe to userFills instead of order updates ([1fbe376](https://github.com/herenow/atomic-gtw/commit/1fbe376c6affeb70186a8fc3f4ca26f8ac15dc7f))
* **hyperliquid:** Trade px/amount was inverted ([b15ca82](https://github.com/herenow/atomic-gtw/commit/b15ca828152abf63558f55b7e8abca237f2c5ec8))

## [1.54.0](https://github.com/herenow/atomic-gtw/compare/v1.53.2...v1.54.0) (2025-05-19)


### ✨ Features

* **cryptocom:** Identify futures contract market ([23898a6](https://github.com/herenow/atomic-gtw/commit/23898a69c128852b8bcfa81bbe48fcf9fd489b79))

## [1.53.2](https://github.com/herenow/atomic-gtw/compare/v1.53.1...v1.53.2) (2025-05-14)


### 🐛 Bug Fixes

* **mexc:** Unused fromVersion var ([25ce57d](https://github.com/herenow/atomic-gtw/commit/25ce57d535fb9ab213ff4e0b0ef741cbb92b70fd))

## [1.53.1](https://github.com/herenow/atomic-gtw/compare/v1.53.0...v1.53.1) (2025-05-14)


### 📦 Chores

* **mexc:** Remove version check on market data ([e8c12de](https://github.com/herenow/atomic-gtw/commit/e8c12ded1f0b79b6fde7a89204ee78dea535922d))

## [1.53.0](https://github.com/herenow/atomic-gtw/compare/v1.52.0...v1.53.0) (2025-05-14)


### ✨ Features

* **mexc:** Websocket stream market data ([3d8cc63](https://github.com/herenow/atomic-gtw/commit/3d8cc63046d6620a1b11468c02e996606a93a449))


### 🐛 Bug Fixes

* **mexc:** Proper depth queue handling ([3c6f299](https://github.com/herenow/atomic-gtw/commit/3c6f2997b5914b1c797dd1a88e21d21962d9b840))

## [1.52.0](https://github.com/herenow/atomic-gtw/compare/v1.51.7...v1.52.0) (2025-05-14)


### ✨ Features

* add auth headers to websocket, add ping-pong and custom unmarshal to wsMessage to handle different data fields ([9d6c721](https://github.com/herenow/atomic-gtw/commit/9d6c72106e229c9cf695f9dc18607816db39ca1f))
* **valr:** Implement VALR exchange integration ([dc8f048](https://github.com/herenow/atomic-gtw/commit/dc8f0485df2d779283da7859b9c49d795892d1d3))


### 📦 Chores

* update logs to add exchange name ([5be3e89](https://github.com/herenow/atomic-gtw/commit/5be3e89f09ad7387bf2eeb65a2f53c51a81b534a))


### ♻️ Refactor

* **valr:** Create authentication header helper function for API requests ([5f9a8e5](https://github.com/herenow/atomic-gtw/commit/5f9a8e597bd36fdfd77e7a606a71037cce01768a))
* **valr:** Enhance APIOrder structure and OpenOrders method to include additional order details ([757e47e](https://github.com/herenow/atomic-gtw/commit/757e47e1fcb60c9ac7957148af46274d340ed5c8))
* **valr:** Remove order status update handling from websocket messages ([244bd45](https://github.com/herenow/atomic-gtw/commit/244bd45b0cae20e9847c3725c7c122e3232f4927))
* **valr:** Simplify websocket connection and message handling, improve error logging ([ef99ffb](https://github.com/herenow/atomic-gtw/commit/ef99ffba14a2150372c2b2f43ee87e19176e6f40))
* **valr:** Update API order endpoints and enhance error handling for order placement ([27ccdf7](https://github.com/herenow/atomic-gtw/commit/27ccdf7703868448f8892172af6ea7d3fd428421))
* **valr:** Update GetOpenOrders to use URL parsing for query parameters ([89c4eb0](https://github.com/herenow/atomic-gtw/commit/89c4eb0bb60248ef5dd44f05d9a4efd32e9471a1))
* **valr:** Update wsOrderBook to use gateway.PriceArray ([b67255d](https://github.com/herenow/atomic-gtw/commit/b67255ddebdf9db21459b008d0159bcb3762c9d9))

## [1.51.7](https://github.com/herenow/atomic-gtw/compare/v1.51.6...v1.51.7) (2025-05-01)


### 🐛 Bug Fixes

* **bitfinex:** Price tick ([e26cbd8](https://github.com/herenow/atomic-gtw/commit/e26cbd82298a8baccedfa4b51b6e7c3e7415840e))

## [1.51.6](https://github.com/herenow/atomic-gtw/compare/v1.51.5...v1.51.6) (2025-04-30)


### 🐛 Bug Fixes

* **bitfinex:** Math abs on fee ([6c40eb1](https://github.com/herenow/atomic-gtw/commit/6c40eb17f84b68da0f8569f9f391b5783d33c750))

## [1.51.5](https://github.com/herenow/atomic-gtw/compare/v1.51.4...v1.51.5) (2025-04-30)


### 🐛 Bug Fixes

* **bitfinex:** Execution tracking ([8c23bb7](https://github.com/herenow/atomic-gtw/commit/8c23bb786e86c3dd9bba1c3507742afca07d5fbb))

## [1.51.4](https://github.com/herenow/atomic-gtw/compare/v1.51.3...v1.51.4) (2025-04-30)


### 🐛 Bug Fixes

* **bitfinex:** Remove bitfinex-api-go dependency ([1a38025](https://github.com/herenow/atomic-gtw/commit/1a3802524c4a3dd075c7c474fe6ef6acf5f8df27))
* **book_viwer:** Amount tick was not being used ([0e127c7](https://github.com/herenow/atomic-gtw/commit/0e127c7fc5c8f9739ebbf1561f44b2cfefeaa844))

## [1.51.3](https://github.com/herenow/atomic-gtw/compare/v1.51.2...v1.51.3) (2025-04-30)


### 📦 Chores

* **bitfinex:** Remove unused methods and migrate to gateway.Fill ([151138c](https://github.com/herenow/atomic-gtw/commit/151138cb41f2bb39b0a8c5e99bec549def6f96c7))

## [1.51.2](https://github.com/herenow/atomic-gtw/compare/v1.51.1...v1.51.2) (2025-04-29)


### 🐛 Bug Fixes

* **bitfinex:** Add missing assetTranslateToCommon to Balances() ([639a14c](https://github.com/herenow/atomic-gtw/commit/639a14cf3a1bffa46a81934cf753d5983c345489))

## [1.51.1](https://github.com/herenow/atomic-gtw/compare/v1.51.0...v1.51.1) (2025-04-28)


### 🐛 Bug Fixes

* **binancefuures:** GetSymbols different endpoint on fapi ([beabfe8](https://github.com/herenow/atomic-gtw/commit/beabfe854d089523019bef114b400870c1363a17))

## [1.51.0](https://github.com/herenow/atomic-gtw/compare/v1.50.2...v1.51.0) (2025-04-23)


### ✨ Features

* **bitfinex:** GetDepthBook implementation ([b4da72e](https://github.com/herenow/atomic-gtw/commit/b4da72e159dd16e51f6b993345db54dfc80ebc29))

## [1.50.2](https://github.com/herenow/atomic-gtw/compare/v1.50.1...v1.50.2) (2025-04-23)


### 📦 Chores

* **bitfinex:** Treat no slice errors on empty responses ([7be753f](https://github.com/herenow/atomic-gtw/commit/7be753f89ab217e7a081bd2d119d638b7f9b42c7))

## [1.50.1](https://github.com/herenow/atomic-gtw/compare/v1.50.0...v1.50.1) (2025-04-21)


### 🐛 Bug Fixes

* **gatway:** Fix and improve chain id init ([a26c7e0](https://github.com/herenow/atomic-gtw/commit/a26c7e04dd4f3f941679949ac0cede3bbc37667a))

## [1.50.0](https://github.com/herenow/atomic-gtw/compare/v1.49.0...v1.50.0) (2025-04-21)


### ✨ Features

* **uniswapv3:** Rename to token opts and cleanup ([d8f7115](https://github.com/herenow/atomic-gtw/commit/d8f7115b03515c08d7375314a6800692f2130076))

## [1.49.0](https://github.com/herenow/atomic-gtw/compare/v1.48.1...v1.49.0) (2025-04-21)


### ✨ Features

* **uniswapv3:** Properly handle different chains ([acd5e8f](https://github.com/herenow/atomic-gtw/commit/acd5e8fc7174df039db52d4d6b0a522a8d870144))

## [1.48.1](https://github.com/herenow/atomic-gtw/compare/v1.48.0...v1.48.1) (2025-04-21)


### 🐛 Bug Fixes

* **uniswapv3:** Missing token cache file ([af15a92](https://github.com/herenow/atomic-gtw/commit/af15a9221edd77fae120d0af3469d05a01262086))

## [1.48.0](https://github.com/herenow/atomic-gtw/compare/v1.47.28...v1.48.0) (2025-04-21)


### ✨ Features

* **uniswapv3:** Uniswap v3 basic integration ([83f3996](https://github.com/herenow/atomic-gtw/commit/83f3996c7fc904004becd1f9b92b960c7f2e9712))

## [1.47.28](https://github.com/herenow/atomic-gtw/compare/v1.47.27...v1.47.28) (2025-04-18)


### 📦 Chores

* **mexc:** Set PostOnly as LIMIT_MAKER ([3955229](https://github.com/herenow/atomic-gtw/commit/3955229e954a55f17d990afcd969f415c117be33))

## [1.47.27](https://github.com/herenow/atomic-gtw/compare/v1.47.26...v1.47.27) (2025-04-18)


### 📦 Chores

* **mexc:** Missing balance match ([bfefe77](https://github.com/herenow/atomic-gtw/commit/bfefe7759e257e34faca5652d1200c291497fac3))

## [1.47.26](https://github.com/herenow/atomic-gtw/compare/v1.47.25...v1.47.26) (2025-04-18)


### 🐛 Bug Fixes

* **coinbase:** Trades processing ([25fb047](https://github.com/herenow/atomic-gtw/commit/25fb0474b7a5d5a21ad469b0d95c4ffde6cda714))

## [1.47.25](https://github.com/herenow/atomic-gtw/compare/v1.47.24...v1.47.25) (2025-04-18)


### 🐛 Bug Fixes

* **coinbase:** Missing market_trades sub ([cb6c01b](https://github.com/herenow/atomic-gtw/commit/cb6c01b2cf6c543aec1113c8a356959e950dc8be))

## [1.47.24](https://github.com/herenow/atomic-gtw/compare/v1.47.23...v1.47.24) (2025-04-18)


### 📦 Chores

* **mexc:** Map balance and rate limit errs ([5e1f0da](https://github.com/herenow/atomic-gtw/commit/5e1f0da668945c7754011c8ebf85d9a626afbf24))

## [1.47.23](https://github.com/herenow/atomic-gtw/compare/v1.47.22...v1.47.23) (2025-04-17)


### 🐛 Bug Fixes

* **utils:** FloorToTick handle negative values ([fd4fc17](https://github.com/herenow/atomic-gtw/commit/fd4fc176ff2263ae6127d3bb3622f2169991a46d))

## [1.47.22](https://github.com/herenow/atomic-gtw/compare/v1.47.21...v1.47.22) (2025-04-17)


### 🐛 Bug Fixes

* **utils:** FloorToTick was incorrect ([9350a18](https://github.com/herenow/atomic-gtw/commit/9350a183ee3febee5870a8c80c97eff23d36b64f))

## [1.47.21](https://github.com/herenow/atomic-gtw/compare/v1.47.20...v1.47.21) (2025-04-14)


### 🐛 Bug Fixes

* **cryptocom:** Market data gateway default processor ([0780ae2](https://github.com/herenow/atomic-gtw/commit/0780ae2c1543b3c1e0903a535a19bacf49c7067e))

## [1.47.20](https://github.com/herenow/atomic-gtw/compare/v1.47.19...v1.47.20) (2025-04-14)


### 🐛 Bug Fixes

* **cryptocom:** Respond to heartbeat requests ([208ebd1](https://github.com/herenow/atomic-gtw/commit/208ebd1f4df1135519f156329759d389671dc6a8))

## [1.47.19](https://github.com/herenow/atomic-gtw/compare/v1.47.18...v1.47.19) (2025-04-14)


### 📦 Chores

* **cryptocom:** Map min order size err ([3b198b2](https://github.com/herenow/atomic-gtw/commit/3b198b27abe50686e1c1d2d5fdf1f4b9c4f8cf6e))

## [1.47.18](https://github.com/herenow/atomic-gtw/compare/v1.47.17...v1.47.18) (2025-04-14)


### 🐛 Bug Fixes

* **crypto:** Slice/array signing ([2d4ef61](https://github.com/herenow/atomic-gtw/commit/2d4ef61b56b971a2faff34da4be42779191e5fc7))

## [1.47.17](https://github.com/herenow/atomic-gtw/compare/v1.47.16...v1.47.17) (2025-04-14)


### 📦 Chores

* **cryptocom:** Allow tagged cids ([43c9621](https://github.com/herenow/atomic-gtw/commit/43c9621d1d890eaf7431bbcbcfb94c24db419dbb))

## [1.47.16](https://github.com/herenow/atomic-gtw/compare/v1.47.15...v1.47.16) (2025-04-14)


### 📦 Chores

* **bitfinex:** Fix indentation ([b1668e1](https://github.com/herenow/atomic-gtw/commit/b1668e1d028aa24b86e3e79318fb332e0ba3b903))


### 🐛 Bug Fixes

* **utils:** FloorToTick ([5d1510c](https://github.com/herenow/atomic-gtw/commit/5d1510c1559a62b430804f0b76a2831018225789))

## [1.47.15](https://github.com/herenow/atomic-gtw/compare/v1.47.14...v1.47.15) (2025-04-14)


### 📦 Chores

* **cryptocom:** Rename exchange name ([7dc3c8f](https://github.com/herenow/atomic-gtw/commit/7dc3c8f06c46fb3d5cc8740dc036daaca751555c))
* **gateway:** Allow only alphanumerical on exchange symbol ([111f801](https://github.com/herenow/atomic-gtw/commit/111f80168829fcc09da69e1a25ade6de5536b12a))

## [1.47.14](https://github.com/herenow/atomic-gtw/compare/v1.47.13...v1.47.14) (2025-04-08)


### 🐛 Bug Fixes

* **crypto:** Update from deprecated v2 API to v1 ([ccd2e0a](https://github.com/herenow/atomic-gtw/commit/ccd2e0a4a1e89505bd209e4a391226e190be08b5))

## [1.47.13](https://github.com/herenow/atomic-gtw/compare/v1.47.12...v1.47.13) (2025-04-04)


### 🐛 Bug Fixes

* **coinstore:** Parse ordID as json.Number ([9d6f377](https://github.com/herenow/atomic-gtw/commit/9d6f377f29a531a2d3785eb6a0019fcc056207f1))

## [1.47.12](https://github.com/herenow/atomic-gtw/compare/v1.47.11...v1.47.12) (2025-04-04)


### 🐛 Bug Fixes

* **coinstore:** GetOrder parsing is not a list as described in API docs ([0d443fd](https://github.com/herenow/atomic-gtw/commit/0d443fd3067afb9bcf099c604cd42a88fec5d708))

## [1.47.11](https://github.com/herenow/atomic-gtw/compare/v1.47.10...v1.47.11) (2025-04-04)


### 📦 Chores

* **coinstore:** Poll for order updates ([7026538](https://github.com/herenow/atomic-gtw/commit/70265380aca332b34b4523a44e4a761a65dd7434))

## [1.47.10](https://github.com/herenow/atomic-gtw/compare/v1.47.9...v1.47.10) (2025-04-01)


### 🕐 Reverts

* Revert "fix(mercadobitcoin): Use PriceScale as price tick" ([952921d](https://github.com/herenow/atomic-gtw/commit/952921d8e557744618622b05c6b11384636666b4))

## [1.47.9](https://github.com/herenow/atomic-gtw/compare/v1.47.8...v1.47.9) (2025-04-01)


### 🐛 Bug Fixes

* **mercadobitcoin:** Use PriceScale as price tick ([fba6d64](https://github.com/herenow/atomic-gtw/commit/fba6d64f0418c974deff5814858629195cd4435b))

## [1.47.8](https://github.com/herenow/atomic-gtw/compare/v1.47.7...v1.47.8) (2025-03-25)


### 🐛 Bug Fixes

* **bybit:** Use options proxies on WS ([9371b01](https://github.com/herenow/atomic-gtw/commit/9371b01095a241358adefde1090ab0753589361a))

## [1.47.7](https://github.com/herenow/atomic-gtw/compare/v1.47.6...v1.47.7) (2025-03-20)


### 📦 Chores

* **mercadobitcoin:** Remove BBLL custom amount tick ([4fefe23](https://github.com/herenow/atomic-gtw/commit/4fefe233b9667d068a871c328a08080305644fd3))

## [1.47.6](https://github.com/herenow/atomic-gtw/compare/v1.47.5...v1.47.6) (2025-03-19)


### 📦 Chores

* **coinstore:** Fallback markets ([d562fd7](https://github.com/herenow/atomic-gtw/commit/d562fd7494f2450f1289cd3cd1849c024f6adfba))

## [1.47.5](https://github.com/herenow/atomic-gtw/compare/v1.47.4...v1.47.5) (2025-03-19)


### 🐛 Bug Fixes

* **coinstore:** Special request for spot symbols ([73ccb9c](https://github.com/herenow/atomic-gtw/commit/73ccb9c66da25529e09c9805647d6a8c34319e5f))

## [1.47.4](https://github.com/herenow/atomic-gtw/compare/v1.47.3...v1.47.4) (2025-03-19)


### 📦 Chores

* **coinstore:** Allow to set custom user agent ([4512730](https://github.com/herenow/atomic-gtw/commit/45127300deb38d5a4395bb55bd72f5b8ac6df32f))

## [1.47.3](https://github.com/herenow/atomic-gtw/compare/v1.47.2...v1.47.3) (2025-03-19)


### 🐛 Bug Fixes

* **coinstore:** Use proper spot symbols API ([915ee95](https://github.com/herenow/atomic-gtw/commit/915ee95a31979cba94c84f27149745cf0c61ff9f))

## [1.47.2](https://github.com/herenow/atomic-gtw/compare/v1.47.1...v1.47.2) (2025-03-18)


### 📦 Chores

* **mercadobitcoin:** Custom amount tick for BBLL ([e8bc432](https://github.com/herenow/atomic-gtw/commit/e8bc432d053a6288681f738dae1f02430cf9f872))

## [1.47.1](https://github.com/herenow/atomic-gtw/compare/v1.47.0...v1.47.1) (2025-03-17)


### 🐛 Bug Fixes

* **coinstore:** Incremental trade update process ([7e1d43c](https://github.com/herenow/atomic-gtw/commit/7e1d43c25d9ae3502595add9c0847545c0989cc9))

## [1.47.0](https://github.com/herenow/atomic-gtw/compare/v1.46.5...v1.47.0) (2025-03-17)


### ✨ Features

* **coinstore:** Integrate market data via websocket ([5cd6919](https://github.com/herenow/atomic-gtw/commit/5cd691932a0e490265fd86f0df99e9dfa252ad82))


### 📦 Chores

* **xt:** Panic when ping fails ([df0bc46](https://github.com/herenow/atomic-gtw/commit/df0bc46e68d8f61c3e9087aec23f91951c273852))

## [1.46.5](https://github.com/herenow/atomic-gtw/compare/v1.46.4...v1.46.5) (2025-03-14)


### 🐛 Bug Fixes

* **coinstore:** GetDepthBook was using invalid API ([16c6578](https://github.com/herenow/atomic-gtw/commit/16c65780eca2b48001547b8087038049a8366bc8))

## [1.46.4](https://github.com/herenow/atomic-gtw/compare/v1.46.3...v1.46.4) (2025-03-11)


### 📦 Chores

* **xt:** Improve messageHandler topic parsing ([f738cdc](https://github.com/herenow/atomic-gtw/commit/f738cdc6fd3e535876429d56e9515368413a4bb6))


### 🐛 Bug Fixes

* **xt:** Market data fixes ([ac96a44](https://github.com/herenow/atomic-gtw/commit/ac96a44379131f06f7b5f444ac0508578415cbbd))

## [1.46.3](https://github.com/herenow/atomic-gtw/compare/v1.46.2...v1.46.3) (2025-03-11)


### 🐛 Bug Fixes

* **xt:** Trade OrderID is a string not int ([f2f53e8](https://github.com/herenow/atomic-gtw/commit/f2f53e83f7ba4d6f0a99490759de71e94231c0e2))

## [1.46.2](https://github.com/herenow/atomic-gtw/compare/v1.46.1...v1.46.2) (2025-03-11)


### 🐛 Bug Fixes

* **xt:** Start heartBeat on account websocket ([3765a8f](https://github.com/herenow/atomic-gtw/commit/3765a8f151e102613a2b526c66779415918c082d))

## [1.46.1](https://github.com/herenow/atomic-gtw/compare/v1.46.0...v1.46.1) (2025-03-11)


### 🐛 Bug Fixes

* **xt:** Remove subscribe from unused topic ([5af4841](https://github.com/herenow/atomic-gtw/commit/5af48419b0df04ede16a38d365208d08565b6bad))

## [1.46.0](https://github.com/herenow/atomic-gtw/compare/v1.45.5...v1.46.0) (2025-03-11)


### ✨ Features

* fix xt marketdata integration ([0c09bd0](https://github.com/herenow/atomic-gtw/commit/0c09bd0258778d1910407d1dd620cfc779af83e6))


### 📦 Chores

* adjust PR comments ([cc04cbe](https://github.com/herenow/atomic-gtw/commit/cc04cbef2fa10e37248c2f360121be8f7d928ebb))
* **xt:** MktData seq mgmt and Optimize Order Book Handling ([2e96f43](https://github.com/herenow/atomic-gtw/commit/2e96f43f68b44073cdd905b6c9c40d9a2dcdff92))


### 🐛 Bug Fixes

* **xt:** Indentation with tabs instead of spaces ([68218ba](https://github.com/herenow/atomic-gtw/commit/68218ba1ce6e34af41ef18a5a457d0cdd89db79d))
* **xt:** remove handle order updates ([fa82360](https://github.com/herenow/atomic-gtw/commit/fa82360713144d3b2633d069dbdf297a74d213ad))

## [1.45.5](https://github.com/herenow/atomic-gtw/compare/v1.45.4...v1.45.5) (2025-03-10)


### 📦 Chores

* **http_client:** Performance adjusments ([7af7a31](https://github.com/herenow/atomic-gtw/commit/7af7a31c514af6671cecda82d170a5a36e6c2513)), closes [/github.com/golang/go/issues/71308#issuecomment-2671091796](https://github.com/herenow//github.com/golang/go/issues/71308/issues/issuecomment-2671091796)

## [1.45.4](https://github.com/herenow/atomic-gtw/compare/v1.45.3...v1.45.4) (2025-03-10)


### 📦 Chores

* **http_client:** Performance adjusments ([9ee1a00](https://github.com/herenow/atomic-gtw/commit/9ee1a00e539c8ec97fb9912deb3f26b052e88b31)), closes [/github.com/golang/go/issues/71308#issuecomment-2671091796](https://github.com/herenow//github.com/golang/go/issues/71308/issues/issuecomment-2671091796)

## [1.45.3](https://github.com/herenow/atomic-gtw/compare/v1.45.2...v1.45.3) (2025-02-25)


### 🐛 Bug Fixes

* **foxbit:** Out of index slice ([d203fc5](https://github.com/herenow/atomic-gtw/commit/d203fc5c5270162293648960609ff5d2e8940f81))

## [1.45.2](https://github.com/herenow/atomic-gtw/compare/v1.45.1...v1.45.2) (2025-02-24)


### 🐛 Bug Fixes

* **safegateway:** Implement a limited err buffer ([58a4d5d](https://github.com/herenow/atomic-gtw/commit/58a4d5d3729aa3afdeaecde4463b15d3c1141d89))

## [1.45.1](https://github.com/herenow/atomic-gtw/compare/v1.45.0...v1.45.1) (2025-02-24)


### 🐛 Bug Fixes

* **options:** URL marshaling to strings ([eb19015](https://github.com/herenow/atomic-gtw/commit/eb190159524d0b572781b150aefcdb4f09c5eff1))


### 📦 Chores

* **kraken:** Remove excessive logging ([04f8a94](https://github.com/herenow/atomic-gtw/commit/04f8a9403231fcbcb8f70b0a1aaeefdb827cdd97))

## [1.45.0](https://github.com/herenow/atomic-gtw/compare/v1.44.0...v1.45.0) (2025-02-14)


### ✨ Features

* **mercadobitcoin:** Fetch initial orderbook from REST ([590b017](https://github.com/herenow/atomic-gtw/commit/590b017bd136f0d1a89c3b19608ed454f7f9f4a6))

## [1.44.0](https://github.com/herenow/atomic-gtw/compare/v1.43.0...v1.44.0) (2025-02-13)


### ✨ Features

* **bybit:** Map minimum order money value ([1f1aabf](https://github.com/herenow/atomic-gtw/commit/****************************************))

## [1.43.0](https://github.com/herenow/atomic-gtw/compare/v1.42.1...v1.43.0) (2025-02-13)


### ✨ Features

* **bybit:** Map min order size errors ([0e07219](https://github.com/herenow/atomic-gtw/commit/0e07219c3199f7834a3373d55571fd3eee8c8e64))

## [1.42.1](https://github.com/herenow/atomic-gtw/compare/v1.42.0...v1.42.1) (2025-02-11)


### 🐛 Bug Fixes

* **mercadobitcoin:** Remove impossible to fetch markets ([7c1d08c](https://github.com/herenow/atomic-gtw/commit/7c1d08c237e578f1b69796d0dd924c3e8fc45684))

## [1.42.0](https://github.com/herenow/atomic-gtw/compare/v1.41.6...v1.42.0) (2025-01-20)


### ✨ Features

* **arbitrum:** add infura integration ([df6c4c7](https://github.com/herenow/atomic-gtw/commit/df6c4c7f93c0240936c70e47b7f1c16a2aa698b3))
* **arbitrum:** add vault supply integration ([3e64673](https://github.com/herenow/atomic-gtw/commit/3e646737d265e563af56a032ecdb43a2ff2f669e))


### 🐛 Bug Fixes

* **arbitrumsupply:** Move init to connect ([637b83b](https://github.com/herenow/atomic-gtw/commit/637b83b5fed51b19be3855e6db513f0824cf796d))
* go mod tidy with unused packages ([2da91c7](https://github.com/herenow/atomic-gtw/commit/2da91c7fa256a1fb5a61c8ac2e792987836762ee))

## [1.41.6](https://github.com/herenow/atomic-gtw/compare/v1.41.5...v1.41.6) (2025-01-15)


### 🐛 Bug Fixes

* **binance:** Quick fix for binance user data streams ([e35cd60](https://github.com/herenow/atomic-gtw/commit/e35cd603d6617b77054b3797017a9069de51faac))

## [1.41.5](https://github.com/herenow/atomic-gtw/compare/v1.41.4...v1.41.5) (2025-01-15)


### 🐛 Bug Fixes

* **binance:** Fix user data stream private endpoints ([7dba098](https://github.com/herenow/atomic-gtw/commit/7dba09899fb542d2007f4538e64c161ae1cb1ff4))

## [1.41.4](https://github.com/herenow/atomic-gtw/compare/v1.41.3...v1.41.4) (2025-01-14)


### 🐛 Bug Fixes

* **bitmart:** Float strings are coming as empty string ([f5690c0](https://github.com/herenow/atomic-gtw/commit/f5690c05f86c922cc9eb34d4998ad79d8e0de860))

## [1.41.3](https://github.com/herenow/atomic-gtw/compare/v1.41.2...v1.41.3) (2025-01-11)


### 📦 Chores

* **cmd:** Allow for use of proxies ([e62473f](https://github.com/herenow/atomic-gtw/commit/e62473f0125b1f146d715381d63e704fe671a0f4))

## [1.41.2](https://github.com/herenow/atomic-gtw/compare/v1.41.1...v1.41.2) (2025-01-11)


### 🐛 Bug Fixes

* **hyperliquid:** Array len initialized by mistake ([9524149](https://github.com/herenow/atomic-gtw/commit/95241494516d37a7011f2409283389bce5e09f79))

## [1.41.1](https://github.com/herenow/atomic-gtw/compare/v1.41.0...v1.41.1) (2025-01-11)


### 🐛 Bug Fixes

* **crypto:** Trade timestamp parsing ([6fcb9da](https://github.com/herenow/atomic-gtw/commit/6fcb9da6902bc56f68154c959319d57861079f56))

## [1.41.0](https://github.com/herenow/atomic-gtw/compare/v1.40.2...v1.41.0) (2025-01-10)


### ✨ Features

* **binance:** Use specific market data endpoint ([4722324](https://github.com/herenow/atomic-gtw/commit/47223242da26aead8881c94aa9a23b5a84a0bfdd))

## [1.40.2](https://github.com/herenow/atomic-gtw/compare/v1.40.1...v1.40.2) (2025-01-09)


### 🐛 Bug Fixes

* **lbank:** Trade tracking incorrect field ([749ff7f](https://github.com/herenow/atomic-gtw/commit/749ff7f99bdea7948957b5df04997e3766fad955))

## [1.40.1](https://github.com/herenow/atomic-gtw/compare/v1.40.0...v1.40.1) (2025-01-09)


### 🐛 Bug Fixes

* **coinbase:** Market depth data was being processed incorrectly ([3ce30b2](https://github.com/herenow/atomic-gtw/commit/3ce30b26fb515ce148a73a5e5f451852de7a6ef4))

## [1.40.0](https://github.com/herenow/atomic-gtw/compare/v1.39.3...v1.40.0) (2025-01-09)


### 🐛 Bug Fixes

* **release:** Force release ([fad9ea4](https://github.com/herenow/atomic-gtw/commit/fad9ea4bd8fc66d9212bdf8a860eadd53d77fa38))


### 📦 Chores

* **release:** 1.39.4 [skip ci] ([30056c7](https://github.com/herenow/atomic-gtw/commit/30056c761eca4c513d022ddc1840b9106d85a205))
* **release:** 1.39.4 [skip ci] ([0699b62](https://github.com/herenow/atomic-gtw/commit/0699b6232dd92d7353d3ea0822938bc3e2eb0488))
* **release:** 1.39.4 [skip ci] ([cbb290b](https://github.com/herenow/atomic-gtw/commit/cbb290b6efbc9f9e9b5da69d188441cd92fc834d))
* **safegateway:** Don't panic on process message errors ([40cc2b9](https://github.com/herenow/atomic-gtw/commit/40cc2b9c3bed2e4b3576d3403973ee072a63720b))
* **xt:** Map possible send order errors ([ba75b71](https://github.com/herenow/atomic-gtw/commit/ba75b7119d627b4a5e354b2ea5edcf7cc2a2c696))


### ✨ Features

* **release:** Force release we are stuck at 1.39.4 ([f47d448](https://github.com/herenow/atomic-gtw/commit/f47d448512cce5214c108c1559c700c23f417e10))

## [1.39.4](https://github.com/herenow/atomic-gtw/compare/v1.39.3...v1.39.4) (2025-01-09)


### 📦 Chores

* **release:** 1.39.4 [skip ci] ([0699b62](https://github.com/herenow/atomic-gtw/commit/0699b6232dd92d7353d3ea0822938bc3e2eb0488))
* **release:** 1.39.4 [skip ci] ([cbb290b](https://github.com/herenow/atomic-gtw/commit/cbb290b6efbc9f9e9b5da69d188441cd92fc834d))
* **safegateway:** Don't panic on process message errors ([40cc2b9](https://github.com/herenow/atomic-gtw/commit/40cc2b9c3bed2e4b3576d3403973ee072a63720b))
* **xt:** Map possible send order errors ([ba75b71](https://github.com/herenow/atomic-gtw/commit/ba75b7119d627b4a5e354b2ea5edcf7cc2a2c696))


### 🐛 Bug Fixes

* **release:** Force release ([fad9ea4](https://github.com/herenow/atomic-gtw/commit/fad9ea4bd8fc66d9212bdf8a860eadd53d77fa38))

## [1.39.4](https://github.com/herenow/atomic-gtw/compare/v1.39.3...v1.39.4) (2025-01-09)


### 📦 Chores

* **release:** 1.39.4 [skip ci] ([cbb290b](https://github.com/herenow/atomic-gtw/commit/cbb290b6efbc9f9e9b5da69d188441cd92fc834d))
* **safegateway:** Don't panic on process message errors ([40cc2b9](https://github.com/herenow/atomic-gtw/commit/40cc2b9c3bed2e4b3576d3403973ee072a63720b))
* **xt:** Map possible send order errors ([ba75b71](https://github.com/herenow/atomic-gtw/commit/ba75b7119d627b4a5e354b2ea5edcf7cc2a2c696))

## [1.39.4](https://github.com/herenow/atomic-gtw/compare/v1.39.3...v1.39.4) (2025-01-09)


### 📦 Chores

* **xt:** Map possible send order errors ([ba75b71](https://github.com/herenow/atomic-gtw/commit/ba75b7119d627b4a5e354b2ea5edcf7cc2a2c696))

## [1.39.3](https://github.com/herenow/atomic-gtw/compare/v1.39.2...v1.39.3) (2025-01-03)


### 🐛 Bug Fixes

* **bitypreco:** Ignore _debt balances ([9c9bc22](https://github.com/herenow/atomic-gtw/commit/9c9bc226b89334aa38e933da6fa861a8a73c941f))

## [1.39.2](https://github.com/herenow/atomic-gtw/compare/v1.39.1...v1.39.2) (2024-12-17)


### 🐛 Bug Fixes

* **bitso:** Error code matching ([517afd3](https://github.com/herenow/atomic-gtw/commit/517afd34e2dd4f33a8c7abfc3c945934ed990044))

## [1.39.1](https://github.com/herenow/atomic-gtw/compare/v1.39.0...v1.39.1) (2024-12-07)


### 🐛 Bug Fixes

* **safegateway:** Properly handle transport errors ([774ad8e](https://github.com/herenow/atomic-gtw/commit/774ad8e411d2710a9677e98f2f08a6b9ba30c8ce))

## [1.39.0](https://github.com/herenow/atomic-gtw/compare/v1.38.0...v1.39.0) (2024-12-07)


### ✨ Features

* **safegateway:** Support GetDepthBook ([bf6d703](https://github.com/herenow/atomic-gtw/commit/bf6d7034b3afe9c3e95b98e4f180fd81a3ced6b0))

## [1.38.0](https://github.com/herenow/atomic-gtw/compare/v1.37.0...v1.38.0) (2024-12-04)


### 🐛 Bug Fixes

* **book:** typos ([a0e21c7](https://github.com/herenow/atomic-gtw/commit/a0e21c7fe9d0b07318b483b9cdc87f236f1832fa))


### ✨ Features

* **gtwCheck:** implements a basic structure for gtw check ([36efc92](https://github.com/herenow/atomic-gtw/commit/36efc9299ce9afb832316af941ba612e47e3983e))


### 📦 Chores

* **binance:** add avg price and fee to order update events ([a9ffb6e](https://github.com/herenow/atomic-gtw/commit/a9ffb6ed2238a552ae81d0d1cee51355861fa79f))
* **binance:** add staging flag ([70dd5f2](https://github.com/herenow/atomic-gtw/commit/70dd5f2a41f9092576cdc8c0c0ff3f7bb72347af))
* **binance:** added expected errors ([b8453c6](https://github.com/herenow/atomic-gtw/commit/b8453c6af6494301698638122044b83ab13109aa))
* **bitypreco:** add nil return on closing gateway function ([77ccadd](https://github.com/herenow/atomic-gtw/commit/77ccadd3e74943967cdca94b0e959cc31065fcac))
* **errors:** removed initial capitalized errors ([9e12d44](https://github.com/herenow/atomic-gtw/commit/9e12d443883012ddf0a3911530a3e8ddacefc79f))
* **gtw-check:** add list markets and balances check flows ([e8935f3](https://github.com/herenow/atomic-gtw/commit/e8935f32761a33e6281ad560089c6180c702ced5))
* **gtw-check:** add market override ([4538a25](https://github.com/herenow/atomic-gtw/commit/4538a2568560615cac3f22426b64d9c81e3af199))
* **gtw-check:** added tick manager ([0354a75](https://github.com/herenow/atomic-gtw/commit/0354a75f96a27de364e7d8bc1909469b26b0f0df))
* **gtw-check:** adjust check code to support priority markets ([ec24703](https://github.com/herenow/atomic-gtw/commit/ec2470315983177799d2d7e4aeedba1a7e206db1))
* **gtw-check:** adjust formatter for metadata ([73fcd59](https://github.com/herenow/atomic-gtw/commit/73fcd590024ac1dad354ec0c38ede31d10ea2bf0))
* **gtw-check:** adjust main.go file ([28efac9](https://github.com/herenow/atomic-gtw/commit/28efac93ca80b9729ed18d641df302a5db9bc8cf))
* **gtw-check:** check both base/quote balances in market selection ([b24b345](https://github.com/herenow/atomic-gtw/commit/b24b3453bd76aedb36b183ba6eace162492e0350))
* **gtw-check:** code review and some adjustments ([fcbcfa7](https://github.com/herenow/atomic-gtw/commit/fcbcfa7435f91ce7e0accf5bcdbb6bfd9782bd69))
* **gtw-check:** code review insufficient balance ([63bbe09](https://github.com/herenow/atomic-gtw/commit/63bbe092a359d6575bfb47185f54cd4e4df02444))
* **gtw-check:** code review min order size flow ([a708e4b](https://github.com/herenow/atomic-gtw/commit/a708e4b904e2c6d7220771a75b25910b97d357b8))
* **gtw-check:** final slack message template ([4e8a153](https://github.com/herenow/atomic-gtw/commit/4e8a153d7f1ed1e00f4fe5afe044476b8aa681a9))
* **gtw-check:** improve execution flow ([af2feb9](https://github.com/herenow/atomic-gtw/commit/af2feb954f1d2342d9e2ee0b392882016cc63c80))
* **gtw-check:** improve logging and order placement flow ([e723757](https://github.com/herenow/atomic-gtw/commit/e723757f4b49d4fd28d75e52b15e5171b3598bf0))
* **gtw-check:** improve slack and console messages ([e29fed2](https://github.com/herenow/atomic-gtw/commit/e29fed225d6b17d044cbbd2d965e29ede4b66d4c))
* **gtw-check:** improve slack message ([e9edeac](https://github.com/herenow/atomic-gtw/commit/e9edeac8d954f5dfec374cda905fa42134e541fa))
* **gtw-check:** move everything to check package ([8e00590](https://github.com/herenow/atomic-gtw/commit/8e00590712fcc9478c994d9570b39424245338a0))
* **gtw-check:** remove from mkt data old trade validation ([bbc084d](https://github.com/herenow/atomic-gtw/commit/bbc084d5ec080a6d2349fbda7647857d684ce30b))
* **gtw-check:** remove taker and maker fee validations from list mkts ([6a4c6a3](https://github.com/herenow/atomic-gtw/commit/6a4c6a32242cf450779380ed293e4cbe271ebaa2))
* **gtw-check:** remove tick channel from events collector code ([0ba9d09](https://github.com/herenow/atomic-gtw/commit/0ba9d09d7af38d930bf9e78dc3eb9706b477f26a))
* **gtw-check:** rename package to use snake_case instead hi-fen ([d4d9afb](https://github.com/herenow/atomic-gtw/commit/d4d9afbf7ec6b61af67728798017e7306878b6b8))
* **gtw-check:** replace order manager with tick manager on all flows ([df0a688](https://github.com/herenow/atomic-gtw/commit/df0a68865093a99704e0c2d2d043448acbd71170))
* **gtw-check:** split Market and Account flows ([dbd719a](https://github.com/herenow/atomic-gtw/commit/dbd719a49b66967f5b11dd2ac217b1c8a962a197))
* **gtw:** remove unnecessary variable on GetMarkets ([9187bd3](https://github.com/herenow/atomic-gtw/commit/9187bd3db3db15f9e11baac1261073dec446f18c))
* resolve merge conflicts ([2f4ee47](https://github.com/herenow/atomic-gtw/commit/2f4ee47d0f0ef2af32cd106cf2a2e4830dffb500))
* **types:** add string method for execution struct ([fff6cfc](https://github.com/herenow/atomic-gtw/commit/fff6cfc433670362d96f7f77bbecbbb318aa5abe))

## [1.37.0](https://github.com/herenow/atomic-gtw/compare/v1.36.11...v1.37.0) (2024-12-03)


### ✨ Features

* **bitso:** Track fills from market data ([414e481](https://github.com/herenow/atomic-gtw/commit/414e48195e0f3e3d5310bc68600dfc4234d58071))

## [1.36.11](https://github.com/herenow/atomic-gtw/compare/v1.36.10...v1.36.11) (2024-11-27)


### 🐛 Bug Fixes

* **safegateway:** Missing return statement ([896ab38](https://github.com/herenow/atomic-gtw/commit/896ab383a08f346432357a37f7040c4b723a42c5))

## [1.36.10](https://github.com/herenow/atomic-gtw/compare/v1.36.9...v1.36.10) (2024-11-26)


### 📦 Chores

* **safegateway:** Remove reconnect option ([b5ef918](https://github.com/herenow/atomic-gtw/commit/b5ef91858697604676cf9c6760c13f163c480bac))

## [1.36.9](https://github.com/herenow/atomic-gtw/compare/v1.36.8...v1.36.9) (2024-11-26)


### 📦 Chores

* **book_viewer:** Allow to spawn as safeGateway ([8fc0d90](https://github.com/herenow/atomic-gtw/commit/8fc0d90f4742c4bfd2f72c9e7a2b0c5d28ac03ae))
* **hotcoin:** Remove cmd/hotcoin_test ([24f7c58](https://github.com/herenow/atomic-gtw/commit/24f7c58eb9b62b895c851e798c7194ef483432b7))

## [1.36.8](https://github.com/herenow/atomic-gtw/compare/v1.36.7...v1.36.8) (2024-11-25)


### 📦 Chores

* **mercadobitcoin:** Parse price tick and min amounts from API ([65208d2](https://github.com/herenow/atomic-gtw/commit/65208d2fcec597c1c7a01c86311852a2002ca6d9))

## [1.36.7](https://github.com/herenow/atomic-gtw/compare/v1.36.6...v1.36.7) (2024-11-22)


### 📦 Chores

* **binance:** Async fetch orderbook snapshots ([dab704f](https://github.com/herenow/atomic-gtw/commit/dab704f4129f9fd393a4bef2563de20503e4c342))

## [1.36.6](https://github.com/herenow/atomic-gtw/compare/v1.36.5...v1.36.6) (2024-11-22)


### 📦 Chores

* **binance:** Increase maximum tickCh for Binance ([b93b6e1](https://github.com/herenow/atomic-gtw/commit/b93b6e15696cd52f798d93912dae1623a9c1e55b))

## [1.36.5](https://github.com/herenow/atomic-gtw/compare/v1.36.4...v1.36.5) (2024-11-20)


### 📦 Chores

* **okx:** Ping timer rollback to 3 seconds ([1045900](https://github.com/herenow/atomic-gtw/commit/10459002ff8f23156becda9900e47f05e1f01046))

## [1.36.4](https://github.com/herenow/atomic-gtw/compare/v1.36.3...v1.36.4) (2024-11-20)


### 🐛 Bug Fixes

* **okx:** Calling go routines inside markets loop ([4554496](https://github.com/herenow/atomic-gtw/commit/45544964bcd567318e8b698068a70be58088a377))

## [1.36.3](https://github.com/herenow/atomic-gtw/compare/v1.36.2...v1.36.3) (2024-11-20)


### 📦 Chores

* **okx:** Fix okx pinger time and log source of error ([f288256](https://github.com/herenow/atomic-gtw/commit/f288256f255b8d680ab48302946989c762813114))

## [1.36.2](https://github.com/herenow/atomic-gtw/compare/v1.36.1...v1.36.2) (2024-10-27)


### 📦 Chores

* **docs:** Update exmarkets status ([fff5acc](https://github.com/herenow/atomic-gtw/commit/fff5acc3ce9e89621985661a4a96e6f590cb90b2))

## [1.36.1](https://github.com/herenow/atomic-gtw/compare/v1.36.0...v1.36.1) (2024-10-27)


### 📦 Chores

* **gateway:** List and remove deprecated exchanges ([d57dddb](https://github.com/herenow/atomic-gtw/commit/d57dddb3e5aa7cfe21ed8a69435588822b2ab530))

## [1.36.0](https://github.com/herenow/atomic-gtw/compare/v1.35.2...v1.36.0) (2024-10-23)


### ✨ Features

* **binance:** Implement gateway.GetDepthBook ([8a72693](https://github.com/herenow/atomic-gtw/commit/8a726937441ad1258592b869857c2f9800311d96))

## [1.35.2](https://github.com/herenow/atomic-gtw/compare/v1.35.1...v1.35.2) (2024-10-23)


### 📦 Chores

* **test_integration:** Pass on PollAccountData ([d7f686f](https://github.com/herenow/atomic-gtw/commit/d7f686f5521eb7cb1e0af54ae29ba6573057c211))


### 🐛 Bug Fixes

* **binance:** Balances list initialized with empty balances ([fb4ec7c](https://github.com/herenow/atomic-gtw/commit/fb4ec7cc60876a20dfe8b8d8ebee27764faefdd5))

## [1.35.1](https://github.com/herenow/atomic-gtw/compare/v1.35.0...v1.35.1) (2024-10-17)


### 🐛 Bug Fixes

* **gateway:** Proxy tester had a incorrect logic ([1672b45](https://github.com/herenow/atomic-gtw/commit/1672b4522bbe675b5b2fdbb1c26762934033f0e8))

## [1.35.0](https://github.com/herenow/atomic-gtw/compare/v1.34.0...v1.35.0) (2024-10-17)


### ✨ Features

* **gateway:** Improve proxy tester ([c0aa685](https://github.com/herenow/atomic-gtw/commit/c0aa68565a8d734070d3084a54193948b4000fc7))

## [1.34.0](https://github.com/herenow/atomic-gtw/compare/v1.33.0...v1.34.0) (2024-10-17)


### ✨ Features

* **gateway:** Trade metadata ([15c65e0](https://github.com/herenow/atomic-gtw/commit/15c65e05c479e6e29d16d972869b5a975a470238))

## [1.33.0](https://github.com/herenow/atomic-gtw/compare/v1.32.5...v1.33.0) (2024-10-16)


### ✨ Features

* **digitra:** Get fills from websocket ([2ba7686](https://github.com/herenow/atomic-gtw/commit/2ba7686b67852b53026b60325265d60f97f193f3))

## [1.32.5](https://github.com/herenow/atomic-gtw/compare/v1.32.4...v1.32.5) (2024-10-16)


### 📦 Chores

* **bitget:** Include trade timestamp on trade tracking ([5734802](https://github.com/herenow/atomic-gtw/commit/5734802e5b3fe3e6b907481153c3193b5db460a8))

## [1.32.4](https://github.com/herenow/atomic-gtw/compare/v1.32.3...v1.32.4) (2024-10-08)


### 🐛 Bug Fixes

* **ripio:** Proper sequence validation ([a083bd8](https://github.com/herenow/atomic-gtw/commit/a083bd8db3ca1c8a2b11ddf81124e96eb814302e))

## [1.32.3](https://github.com/herenow/atomic-gtw/compare/v1.32.2...v1.32.3) (2024-10-08)


### 🐛 Bug Fixes

* **bitget:** GetDepthBook update to new v2 API ([a12d607](https://github.com/herenow/atomic-gtw/commit/a12d607b58bbdfad48d7c607b078d68faadb4f20)), closes [#167](https://github.com/herenow/atomic-gtw/issues/167)

## [1.32.2](https://github.com/herenow/atomic-gtw/compare/v1.32.1...v1.32.2) (2024-10-01)


### 📦 Chores

* **proxy_tester:** Use a better fallback ([3933d33](https://github.com/herenow/atomic-gtw/commit/3933d33406f9391efa0b602b866b309b5c472dbe))

## [1.32.1](https://github.com/herenow/atomic-gtw/compare/v1.32.0...v1.32.1) (2024-09-28)


### 📦 Chores

* **digitra:** Lower fetch fills page limit ([024e97a](https://github.com/herenow/atomic-gtw/commit/024e97ad043971df834a00b6e0d8f73a3cde796f))

## [1.32.0](https://github.com/herenow/atomic-gtw/compare/v1.31.7...v1.32.0) (2024-09-19)


### 📦 Chores

* **deps:** bump google.golang.org/grpc ([f4b7a82](https://github.com/herenow/atomic-gtw/commit/f4b7a8249e7dc588aee13f96e8dff10d813ade6d))
* **deps:** bump micromatch ([fcc6962](https://github.com/herenow/atomic-gtw/commit/fcc69626f6415bc92538f4557640d6e552b7add4))


### ✨ Features

* **gateway:** Meta property to Executions ([e8736af](https://github.com/herenow/atomic-gtw/commit/e8736af3ab0581002ef7c1a4fd976fdc4c1865ad))
* **mercadobitcoin:** Allow to use ExternalID (ClientOrderID) ([7b7c392](https://github.com/herenow/atomic-gtw/commit/7b7c392eac4e261e7759bb8fc68025c459849f7d))

## [1.31.7](https://github.com/herenow/atomic-gtw/compare/v1.31.6...v1.31.7) (2024-09-06)


### 🐛 Bug Fixes

* **foxbit:** Remove ClientOrderID it only accepts number strings, we use alpha numeric ([f3f0d41](https://github.com/herenow/atomic-gtw/commit/f3f0d412b696b680f85410fb83f6ad4ed141152b))

## [1.31.6](https://github.com/herenow/atomic-gtw/compare/v1.31.5...v1.31.6) (2024-09-06)


### 🐛 Bug Fixes

* **foxbit:** Fix ClientOrderID omit if empty ([a464336](https://github.com/herenow/atomic-gtw/commit/a464336f8b45a83bc4d7913aa811a5c4e0c889e5))

## [1.31.5](https://github.com/herenow/atomic-gtw/compare/v1.31.4...v1.31.5) (2024-09-06)


### 📦 Chores

* **foxbit:** Use PostOnly opt and map ClientOrderID ([4a7eeae](https://github.com/herenow/atomic-gtw/commit/4a7eeae48bca760cde96c26dff5caf846b7183e5))

## [1.31.4](https://github.com/herenow/atomic-gtw/compare/v1.31.3...v1.31.4) (2024-09-04)


### 📦 Chores

* **foxbit:** Map rate limit err ([c452ab3](https://github.com/herenow/atomic-gtw/commit/c452ab3668c9b4fae25e2b25b31408d5b5291438))

## [1.31.3](https://github.com/herenow/atomic-gtw/compare/v1.31.2...v1.31.3) (2024-08-20)


### 🐛 Bug Fixes

* **bitmart:** Market trade timestamp tracking ([a32a572](https://github.com/herenow/atomic-gtw/commit/a32a5722a4c9b823252cba37b2d550521680a8c8))

## [1.31.2](https://github.com/herenow/atomic-gtw/compare/v1.31.1...v1.31.2) (2024-08-16)


### 🐛 Bug Fixes

* **digitra_legacy:** Fix the legacy api code ([8090617](https://github.com/herenow/atomic-gtw/commit/809061765183b6ed4d5582c708973412e3db83c9))

## [1.31.1](https://github.com/herenow/atomic-gtw/compare/v1.31.0...v1.31.1) (2024-08-16)


### 📦 Chores

* **types:** Add ClientOrderID to gateway.Order debug print ([0320079](https://github.com/herenow/atomic-gtw/commit/0320079fd1299ec013218446c3405fc6fc52173f))

## [1.31.0](https://github.com/herenow/atomic-gtw/compare/v1.30.0...v1.31.0) (2024-08-16)


### ✨ Features

* **digitra_legacy:** Allow to use old API integration ([93fe078](https://github.com/herenow/atomic-gtw/commit/93fe0786324309d6e7e72f7eebd660a299425fc1))

## [1.30.0](https://github.com/herenow/atomic-gtw/compare/v1.29.3...v1.30.0) (2024-08-13)


### ✨ Features

* **bybit:** Track exec fee ([aa02939](https://github.com/herenow/atomic-gtw/commit/aa029397f959f687af62aa97945c95f8ec4fc9e0))

## [1.29.3](https://github.com/herenow/atomic-gtw/compare/v1.29.2...v1.29.3) (2024-08-08)


### 🐛 Bug Fixes

* **trubit:** Force release ([624dcd8](https://github.com/herenow/atomic-gtw/commit/624dcd88619b10e671eb3804993ce192edd0b627))

## [1.29.1](https://github.com/herenow/atomic-gtw/compare/v1.29.0...v1.29.1) (2024-08-08)


### 📦 Chores

* **trubit:** Identify "New order rejected" errors ([a04af27](https://github.com/herenow/atomic-gtw/commit/a04af27d5d29e1fbd6dc12e77498b83761ca2e0c))

## [1.29.0](https://github.com/herenow/atomic-gtw/compare/v1.28.3...v1.29.0) (2024-07-27)


### ✨ Features

* **digitra:** Allow for custom base url ([a19a190](https://github.com/herenow/atomic-gtw/commit/a19a190843e52f17b0e7e0fff285e4cc1085ee28))


### 📦 Chores

* **http_client:** Set 15 seconds timeout ([4df1155](https://github.com/herenow/atomic-gtw/commit/4df11553b0ce509d001e587a4bb397722a84ad28))

## [1.28.3](https://github.com/herenow/atomic-gtw/compare/v1.28.2...v1.28.3) (2024-07-23)


### 🐛 Bug Fixes

* **digitra:** Wait order entry typos ([24cb2a8](https://github.com/herenow/atomic-gtw/commit/24cb2a8a20dc2873908b532e6d322a83cfd6d485))

## [1.28.2](https://github.com/herenow/atomic-gtw/compare/v1.28.1...v1.28.2) (2024-07-23)


### 📦 Chores

* **digitra:** Revert waitForOrderEntry to old behavior ([d6e3b27](https://github.com/herenow/atomic-gtw/commit/d6e3b2712fe17aac5be98e80bb7cabc2f7bf0cf0))

## [1.28.1](https://github.com/herenow/atomic-gtw/compare/v1.28.0...v1.28.1) (2024-07-23)


### 📦 Chores

* **digitra:** Remove incorrect test file ([89dad84](https://github.com/herenow/atomic-gtw/commit/89dad84f56d8bdbf791d6a8d88bc05337eb727d9))

## [1.28.0](https://github.com/herenow/atomic-gtw/compare/v1.27.3...v1.28.0) (2024-07-23)


### 🐛 Bug Fixes

* account_gateway methods, wrong data on struct ([30b7be2](https://github.com/herenow/atomic-gtw/commit/30b7be2ebc31bb5d68cf44615bb01d881d502f24))


### ♻️ Refactor

* resolved comments ([35159d7](https://github.com/herenow/atomic-gtw/commit/35159d7894d37d5acf0eb4a8fe665f5cd92d41ea))
* resolved comments + makeHttpRequest enhancement ([71b16a8](https://github.com/herenow/atomic-gtw/commit/71b16a84afc081ccc1b668f8252dfddf9384ec8d))
* splitted api_v1 into a separate file ([c9fe656](https://github.com/herenow/atomic-gtw/commit/c9fe656854c9f45a3ad2acebf7ee7fab5aad93c3))


### ✨ Features

* digitra_v1 new api option ([64edda7](https://github.com/herenow/atomic-gtw/commit/64edda743628bb5c1da73800f73841ed8412c635))
* **digitra:** Digitra v1 integration ([0802b13](https://github.com/herenow/atomic-gtw/commit/0802b13745d084a9b961d58607a84c837fe92e7f))

## [1.27.3](https://github.com/herenow/atomic-gtw/compare/v1.27.2...v1.27.3) (2024-07-20)


### 📦 Chores

* **cmd:** Stop using gtw.Markets() directly ([b53e03e](https://github.com/herenow/atomic-gtw/commit/b53e03e37f879ad6b3ec0e611ba788fc2de21425))


### 🐛 Bug Fixes

* **mercadobitcoin:** Markets list should start empty ([711c656](https://github.com/herenow/atomic-gtw/commit/711c656e87b97ba5a39a5b2c2c382f0858b59037))

## [1.27.2](https://github.com/herenow/atomic-gtw/compare/v1.27.1...v1.27.2) (2024-07-17)


### 📦 Chores

* **bitmart:** Supported methods ([1e50db8](https://github.com/herenow/atomic-gtw/commit/1e50db81854dacdc31f288a3c2e397ff86e7153f))

## [1.27.1](https://github.com/herenow/atomic-gtw/compare/v1.27.0...v1.27.1) (2024-07-17)


### 🐛 Bug Fixes

* **bitmart:** Fix balance not enough error code match ([0490acd](https://github.com/herenow/atomic-gtw/commit/0490acd1ffbdc424f98c8455e859e8757adc1a71))

## [1.27.0](https://github.com/herenow/atomic-gtw/compare/v1.26.6...v1.27.0) (2024-07-17)


### ✨ Features

* **bitmart:** New api endpoints ([674861b](https://github.com/herenow/atomic-gtw/commit/674861b92f2dff5647d9aa6466d628629dc885da))


### 📦 Chores

* **bitmart:** Use error code for matching instead of message ([f24c273](https://github.com/herenow/atomic-gtw/commit/f24c2733cf5b8ee2311b6bb9b137715767c0627b))

## [1.26.6](https://github.com/herenow/atomic-gtw/compare/v1.26.5...v1.26.6) (2024-07-17)


### 📦 Chores

* **mercadobitcoin:** Check for empty orderID ([2996c8c](https://github.com/herenow/atomic-gtw/commit/2996c8c1ef55fe9ce98cdd9869cbd387e02be05f))

## [1.26.5](https://github.com/herenow/atomic-gtw/compare/v1.26.4...v1.26.5) (2024-07-15)


### 🐛 Bug Fixes

* **http_cient:** Randomize first used proxy ([34f8afa](https://github.com/herenow/atomic-gtw/commit/34f8afac24ea457538f243051f43e4084e36c693))

## [1.26.4](https://github.com/herenow/atomic-gtw/compare/v1.26.3...v1.26.4) (2024-07-15)


### 📦 Chores

* **ws_client:** Log which proxy we are using ([9c32ad5](https://github.com/herenow/atomic-gtw/commit/9c32ad5881f4954537cf938eb24160516132247e))

## [1.26.3](https://github.com/herenow/atomic-gtw/compare/v1.26.2...v1.26.3) (2024-07-05)


### 📦 Chores

* **p2pb2b:** Manually add RIB_USDT if not present ([304ae75](https://github.com/herenow/atomic-gtw/commit/304ae75700fde0aac3bd408655a10a115e4ecfc4))

## [1.26.2](https://github.com/herenow/atomic-gtw/compare/v1.26.1...v1.26.2) (2024-07-02)


### 📦 Chores

* **proxy_tester:** Update test url ([5170752](https://github.com/herenow/atomic-gtw/commit/517075217a337d571462a253df361f4651e7c644))

## [1.26.1](https://github.com/herenow/atomic-gtw/compare/v1.26.0...v1.26.1) (2024-06-28)


### 🐛 Bug Fixes

* **lbank:** Trade side mapping ([bdb0c97](https://github.com/herenow/atomic-gtw/commit/bdb0c97df26ee9c5d848abc14312eae901ce4509))
* **lbank:** Trade timestamp parsing and checksum it ([11b4380](https://github.com/herenow/atomic-gtw/commit/11b438061110daafc6cc24dcbcce69879ef16fb7))

## [1.26.0](https://github.com/herenow/atomic-gtw/compare/v1.25.4...v1.26.0) (2024-06-27)


### ✨ Features

* **bybit:** Sign get markets request ([80d2b9a](https://github.com/herenow/atomic-gtw/commit/80d2b9ab0c288d6c5b3f691f4947caf4fc7f040f))

## [1.25.4](https://github.com/herenow/atomic-gtw/compare/v1.25.3...v1.25.4) (2024-06-26)


### 🐛 Bug Fixes

* **p2pb2b:** Public trades tracking ([52b89d9](https://github.com/herenow/atomic-gtw/commit/52b89d9b188374eedcbe065a6f950d83f477abf7))

## [1.25.3](https://github.com/herenow/atomic-gtw/compare/v1.25.2...v1.25.3) (2024-06-26)


### 🐛 Bug Fixes

* **p2pb2b:** Price and amount tick ([308cda3](https://github.com/herenow/atomic-gtw/commit/308cda3a6f28e987b9db03698287821244fa97a6))

## [1.25.2](https://github.com/herenow/atomic-gtw/compare/v1.25.1...v1.25.2) (2024-06-26)


### 🐛 Bug Fixes

* **p2pb2b:** Update balance not enough match ([9b9ffc0](https://github.com/herenow/atomic-gtw/commit/9b9ffc0d41ba084eae64912316f0b862d772d5ca))

## [1.25.1](https://github.com/herenow/atomic-gtw/compare/v1.25.0...v1.25.1) (2024-06-26)


### 🐛 Bug Fixes

* **p2pb2b:** P2PB2B ajudsments ([cccc419](https://github.com/herenow/atomic-gtw/commit/cccc41984c5037d4dbd78035e52d774dc142f54a))

## [1.25.0](https://github.com/herenow/atomic-gtw/compare/v1.24.2...v1.25.0) (2024-06-26)


### ✨ Features

* **trubit:** Allow to set origin_source header ([c344cdf](https://github.com/herenow/atomic-gtw/commit/c344cdffbfee6ebb59b0ed40783c349044a1d65f))

## [1.24.2](https://github.com/herenow/atomic-gtw/compare/v1.24.1...v1.24.2) (2024-06-26)


### 📦 Chores

* **trubit:** Mark as CIDMapping supported ([bf20d12](https://github.com/herenow/atomic-gtw/commit/bf20d12da254abc0c441d04f2a22ecf49aaa9eed))


### 🐛 Bug Fixes

* **trubit:** Better log ws client logs and fix trubit pings ([017e8fe](https://github.com/herenow/atomic-gtw/commit/017e8fe609c65ba598eedea196dccb04d5bd9690))

## [1.24.1](https://github.com/herenow/atomic-gtw/compare/v1.24.0...v1.24.1) (2024-06-25)


### 🐛 Bug Fixes

* **trubit:** Send order was converting price/amount wrong ([532f074](https://github.com/herenow/atomic-gtw/commit/532f07407ae614757834044df11a087fc07eb935))

## [1.24.0](https://github.com/herenow/atomic-gtw/compare/v1.23.1...v1.24.0) (2024-06-25)


### ✨ Features

* trubit integration ([8a65b0e](https://github.com/herenow/atomic-gtw/commit/8a65b0ec85e15a45c9bd7d7df8fb0d24eca6b3f2))


### 🐛 Bug Fixes

* **trubit:** Various fixes to integration ([91c2ff9](https://github.com/herenow/atomic-gtw/commit/91c2ff91ddf4cdb364546da6b021b2de082db7f7))

## [1.23.1](https://github.com/herenow/atomic-gtw/compare/v1.23.0...v1.23.1) (2024-06-25)


### 📦 Chores

* **gateway:** Cleanup errors file syntax ([7b10cf1](https://github.com/herenow/atomic-gtw/commit/7b10cf11832d477615f3cd5d1da77ae423b0b2b0))

## [1.23.0](https://github.com/herenow/atomic-gtw/compare/v1.22.5...v1.23.0) (2024-06-25)


### ✨ Features

* **hyperliquid:** integrate hyperliquid futures dex ([90c65c3](https://github.com/herenow/atomic-gtw/commit/90c65c3cf9336c67baf961616d00cbd7be273692))


### 📦 Chores

* update dependencies ([d140453](https://github.com/herenow/atomic-gtw/commit/d14045304181e06b7b68e988349802f141bfa52d))

## [1.22.5](https://github.com/herenow/atomic-gtw/compare/v1.22.4...v1.22.5) (2024-06-20)


### 🐛 Bug Fixes

* **crypto:** I removed this from the wrong integration ([1846bf3](https://github.com/herenow/atomic-gtw/commit/1846bf31157a1fd91d937673eda097157fe41ff3))

## [1.22.4](https://github.com/herenow/atomic-gtw/compare/v1.22.3...v1.22.4) (2024-06-20)


### 🐛 Bug Fixes

* **cryptomkt:** Forgot to commit this change ([cf2d64c](https://github.com/herenow/atomic-gtw/commit/cf2d64ca04ebb9e258c2f6c7ffbcaf3a2aaf7720))

## [1.22.3](https://github.com/herenow/atomic-gtw/compare/v1.22.2...v1.22.3) (2024-06-20)


### 📦 Chores

* **cryptomkt:** Remove unused symbol on cancel order ([057a97c](https://github.com/herenow/atomic-gtw/commit/057a97c80e590a02655c1429457bd165e9ff7b9a))


### 🐛 Bug Fixes

* **cryptomkt:** Open orders api returning all markets ([066fc3e](https://github.com/herenow/atomic-gtw/commit/066fc3eae5942afb420f2f02a665e558a4a00af2))

## [1.22.2](https://github.com/herenow/atomic-gtw/compare/v1.22.1...v1.22.2) (2024-06-17)


### 🐛 Bug Fixes

* **mercadobitcoin:** Can't create orders with external ID ([49cc374](https://github.com/herenow/atomic-gtw/commit/49cc374c39d4bff358e6e5a81a889167317e97ad))

## [1.22.1](https://github.com/herenow/atomic-gtw/compare/v1.22.0...v1.22.1) (2024-06-17)


### 🐛 Bug Fixes

* **gateway:** Forgot to add SupportedMethods go gtw Interface ([c2e1196](https://github.com/herenow/atomic-gtw/commit/c2e1196a088a0380912fff3f062069b578e2156a))

## [1.22.0](https://github.com/herenow/atomic-gtw/compare/v1.21.3...v1.22.0) (2024-06-17)


### ✨ Features

* **gateway:** Supported methods list and CIDMapping ([0e885c7](https://github.com/herenow/atomic-gtw/commit/0e885c76118a6d7b969ea486b90d4aa37fb084c2))

## [1.21.3](https://github.com/herenow/atomic-gtw/compare/v1.21.2...v1.21.3) (2024-06-17)


### 🐛 Bug Fixes

* **base_gateway:** Missing commit / file ([4ba2406](https://github.com/herenow/atomic-gtw/commit/4ba2406498613d4e4333b5a475d2590f175cc483))

## [1.21.2](https://github.com/herenow/atomic-gtw/compare/v1.21.1...v1.21.2) (2024-06-14)


### 📦 Chores

* **deps:** bump braces in the npm_and_yarn group across 1 directory ([82d3727](https://github.com/herenow/atomic-gtw/commit/82d3727151b2b9a39df3d2267128ee683c66001c))

## [1.21.1](https://github.com/herenow/atomic-gtw/compare/v1.21.0...v1.21.1) (2024-06-14)


### 📦 Chores

* **gateway:** Move BaseGateway to folder ([7d3babb](https://github.com/herenow/atomic-gtw/commit/7d3babb94cde5994a43ede3d6175d610afa47f7a))

## [1.21.0](https://github.com/herenow/atomic-gtw/compare/v1.20.16...v1.21.0) (2024-06-11)


### ✨ Features

* **klever:** add integration for klever exchange ([e0f7de8](https://github.com/herenow/atomic-gtw/commit/e0f7de862a6fe1c50844507ea77d732cfe36e6d2))


### 🐛 Bug Fixes

* **klever:** missing market field when calling orderbook ([8ef09df](https://github.com/herenow/atomic-gtw/commit/8ef09df3ea2da876a3a979be75a5f783cec0eb32))
* **klever:** price and amount tick inverted ([142e3ca](https://github.com/herenow/atomic-gtw/commit/142e3caee4a7b900df6c2a5c8bb29a8f3335f46c))
* **klever:** remove auth from depth endpoint ([d92c1cd](https://github.com/herenow/atomic-gtw/commit/d92c1cdbe28aef6f9f6256ae5b8316c637985454))
* **klever:** symbols to id map plus hardcoded depth decimals ([1e9a9af](https://github.com/herenow/atomic-gtw/commit/1e9a9af38ec183b14ed8b5c812efa2366aca063b))
* **klever:** wrong filled money value input ([58ddea9](https://github.com/herenow/atomic-gtw/commit/58ddea99ed32da44c426230ad042406afaf51d78))


### 📦 Chores

* **klever:** Sort GetDepthBook response ([89c65e3](https://github.com/herenow/atomic-gtw/commit/89c65e384b0044b10a4a35e5e159e3326185791d))

## [1.20.15](https://github.com/herenow/atomic-gtw/compare/v1.20.14...v1.20.15) (2024-06-07)


### 📦 Chores

* **ripio:** Don't log duplicate market data messages ([f1dd9c4](https://github.com/herenow/atomic-gtw/commit/f1dd9c48ffc141cc811854408c51abc7bc7bf217))

## [1.20.14](https://github.com/herenow/atomic-gtw/compare/v1.20.13...v1.20.14) (2024-06-07)


### 🐛 Bug Fixes

* **ripio:** Check for duplicate messages ([f0877eb](https://github.com/herenow/atomic-gtw/commit/f0877eb8c7c6759ed1b4652a90d45dc03c3402ea))

## [1.20.13](https://github.com/herenow/atomic-gtw/compare/v1.20.12...v1.20.13) (2024-06-07)


### 🐛 Bug Fixes

* **bitypreco:** Balances map parsing ([171ea29](https://github.com/herenow/atomic-gtw/commit/171ea291124df81adb4471cb5dd51ea36f1f68ee))

## [1.20.12](https://github.com/herenow/atomic-gtw/compare/v1.20.11...v1.20.12) (2024-06-03)


### 🐛 Bug Fixes

* **novadax:** We need to put the body back after reading it ([9c1f2d0](https://github.com/herenow/atomic-gtw/commit/9c1f2d04953735d61d03dabbaa807259f0e19949))

## [1.20.11](https://github.com/herenow/atomic-gtw/compare/v1.20.10...v1.20.11) (2024-06-03)


### 🐛 Bug Fixes

* **novadax:** Ws trades updated type and return ws error msg content ([38406f4](https://github.com/herenow/atomic-gtw/commit/38406f4c34cd48fc7ceb24022318339a888ca9a1))

## [1.20.10](https://github.com/herenow/atomic-gtw/compare/v1.20.9...v1.20.10) (2024-06-03)


### 📦 Chores

* **novadax:** Match order already closed errs ([7228238](https://github.com/herenow/atomic-gtw/commit/72282386ff57528941fb1ac364cbcb4b19505921))

## [1.20.9](https://github.com/herenow/atomic-gtw/compare/v1.20.8...v1.20.9) (2024-06-03)


### 🐛 Bug Fixes

* **novadax:** Missing file from previous commit ([d1f16fd](https://github.com/herenow/atomic-gtw/commit/d1f16fdc5c5214064278d6f5e35f7fb2b8316f4c))

## [1.20.8](https://github.com/herenow/atomic-gtw/compare/v1.20.7...v1.20.8) (2024-06-03)


### 📦 Chores

* **novadax:** Replace API w/ WebAPI for symbols/book fetching ([cba8d83](https://github.com/herenow/atomic-gtw/commit/cba8d838b1d526f5ef32191020bb758322740e6f))

## [1.20.7](https://github.com/herenow/atomic-gtw/compare/v1.20.6...v1.20.7) (2024-05-31)


### 📦 Chores

* **huobi:** Ignore unnecessary debug log ([063e995](https://github.com/herenow/atomic-gtw/commit/063e9950f2f962e19a18f504176ed5aeb2aa9eed))

## [1.20.6](https://github.com/herenow/atomic-gtw/compare/v1.20.5...v1.20.6) (2024-05-31)


### 🐛 Bug Fixes

* **huobi:** Trades tracking was wrong ([32ae3c8](https://github.com/herenow/atomic-gtw/commit/32ae3c87bb9c5c549fa1385a8b1fc15f4c662aea))

## [1.20.5](https://github.com/herenow/atomic-gtw/compare/v1.20.4...v1.20.5) (2024-05-31)


### 🐛 Bug Fixes

* **gateio:** Trades timestmap json key ([e1b6450](https://github.com/herenow/atomic-gtw/commit/e1b64507f61a1d13da43edb210e6d1f3bed9536b))

## [1.20.4](https://github.com/herenow/atomic-gtw/compare/v1.20.3...v1.20.4) (2024-05-31)


### 🐛 Bug Fixes

* **gateio,huobi:** Incorrect trades timestamp tracking ([d3b1ed4](https://github.com/herenow/atomic-gtw/commit/d3b1ed4ba62d373999cecb7f9b7444ad49c6743f))

## [1.20.3](https://github.com/herenow/atomic-gtw/compare/v1.20.2...v1.20.3) (2024-05-23)


### 📦 Chores

* **cryptomkt:** Map possible send order errs ([fbb44e0](https://github.com/herenow/atomic-gtw/commit/fbb44e0ba29b661bb2ea5bc9beaaab22aa0d756f))

## [1.20.2](https://github.com/herenow/atomic-gtw/compare/v1.20.1...v1.20.2) (2024-05-23)


### 🐛 Bug Fixes

* **crypto,cryptomkt:** Fix mapTradeSide ([589d4da](https://github.com/herenow/atomic-gtw/commit/589d4daae45be81f6207204a8433b77c816d0300))

## [1.20.1](https://github.com/herenow/atomic-gtw/compare/v1.20.0...v1.20.1) (2024-05-23)


### 🐛 Bug Fixes

* **cryptomkt:** Susbcribe to multiple markets ([0f2f787](https://github.com/herenow/atomic-gtw/commit/0f2f787bb26f2003dedd90e4af58b5899a78255b))

## [1.20.0](https://github.com/herenow/atomic-gtw/compare/v1.19.0...v1.20.0) (2024-05-23)


### ✨ Features

* account gateway order requests, market data gateway ws connections ([2c10af4](https://github.com/herenow/atomic-gtw/commit/2c10af412b63f46054054c090e9b410693d24e63))
* account gateway trading ws connection ([96769c8](https://github.com/herenow/atomic-gtw/commit/96769c8ae452b33c80247c7561cabc4d72ae7c9a))
* cryptomkt integration ([8b5606a](https://github.com/herenow/atomic-gtw/commit/8b5606af6a62633415c37bc9bc12e743a4dbfc56))


### ♻️ Refactor

* resolved code review comments ([9a2cd13](https://github.com/herenow/atomic-gtw/commit/9a2cd1334ac7cf6fc1bef69b7dad13cb75559edb))


### 📦 Chores

* **cryptomkt:** Improve depth parsing ([1acafef](https://github.com/herenow/atomic-gtw/commit/1acafef86409684401790fafae816153be44898f))
* **cryptomkt:** Improve mapAPIOrder ([57ae63e](https://github.com/herenow/atomic-gtw/commit/57ae63e6d01a1b87239a47eb9d70c355739ca3cf))
* **cryptomkt:** Improve symbols api parsing ([079b2bc](https://github.com/herenow/atomic-gtw/commit/079b2bc88edfb5107d82c2b9dcf13954496ac048))


### 🐛 Bug Fixes

* **cryptomkt:** All ID should be used as ClientOrderID ([e171a96](https://github.com/herenow/atomic-gtw/commit/e171a96d8e5181d118223adaa20cb451c9bb3874))
* **cryptomkt:** Auth handler ([4a20574](https://github.com/herenow/atomic-gtw/commit/4a205741cc128c0a29a36ba2e9e2223b646f1f1e))
* **cryptomkt:** Exchange name ([13c2c6c](https://github.com/herenow/atomic-gtw/commit/13c2c6c065a51d27ef0fa71756601cd999c0c51b))
* **cryptomkt:** Fix ws report (fill event) ([1dcb3ad](https://github.com/herenow/atomic-gtw/commit/1dcb3ade0a4746b6529d01751ad7b08e520cfa3c))
* **cryptomkt:** MarketDataGateway ([be33905](https://github.com/herenow/atomic-gtw/commit/be339052153d0b13b6cae5674a21d90181fc9938))
* **gateway:** ParseTimestamp ([7465910](https://github.com/herenow/atomic-gtw/commit/746591082710985a8937862fb13c670d1080ef2a))

## [1.19.0](https://github.com/herenow/atomic-gtw/compare/v1.18.0...v1.19.0) (2024-05-17)


### ✨ Features

* **get_depth_book:** depth book handling on bitso, okx, mercadobitcoin ([1ee21b7](https://github.com/herenow/atomic-gtw/commit/1ee21b7a2abff1e4332e20261ffe6cd690f6a277))
* **get_depth_book:** exchange depth methods ([7bd8fc8](https://github.com/herenow/atomic-gtw/commit/7bd8fc81705be021e33021c6cd523745c5d459e3))
* **get_depth_book:** exchange depth methods ([5a21251](https://github.com/herenow/atomic-gtw/commit/5a2125166598b9a3f2f3c91c5d1108266f53ae5f))
* **get_depth_book:** more depth book methods on exchanges ([42ee1e5](https://github.com/herenow/atomic-gtw/commit/42ee1e5ada4643521a110ed7306d80afbc5a16e8))
* **get_depth_book:** more depth book methods on exchanges ([2a54830](https://github.com/herenow/atomic-gtw/commit/2a54830a54f2bb8f544998c43100e12d1e886354))
* **get_depth_book:** more depth book methods on exchanges ([ab88cc8](https://github.com/herenow/atomic-gtw/commit/ab88cc8a02b89eadc4efc8d9a61329c412680d5e))


### 🐛 Bug Fixes

* **get_depth_book:** huobi and ripio request fix ([63f1942](https://github.com/herenow/atomic-gtw/commit/63f1942190ce4896d3d0429718295342205e6ece))


### ♻️ Refactor

* **get_depth_book:** include len check to watch on missing indexes, parseDepth refactor on some exchanges ([7fdce30](https://github.com/herenow/atomic-gtw/commit/7fdce306d26470e55953e5aa2c307ec2da030943))
* **get_depth_book:** include missing params usage on depth requests ([bf0bb8e](https://github.com/herenow/atomic-gtw/commit/bf0bb8e0fe5f77b195346be5f1b11499169196ec))
* **get_depth_book:** parseToDepth method refactor on some exchanges for better readability ([5d6cf2a](https://github.com/herenow/atomic-gtw/commit/5d6cf2ab117331d3c9261663e0fc734c239391ea))
* **get_depth_book:** remove excessive for loops and unecessary code, fix struct ([b7deda3](https://github.com/herenow/atomic-gtw/commit/b7deda380687e7d4738ef2ce765c6c39a581940b))
* **get_depth_book:** remove unecessary error return and unused field on struct ([b395f40](https://github.com/herenow/atomic-gtw/commit/b395f403e221b3fb53cfa49843299dc9832fc443))
* **get_depth_book:** remove unecessary error returns, huobi depth request refactor ([a7d9688](https://github.com/herenow/atomic-gtw/commit/a7d9688b95b8337c5bbd9b73c8ce7e7b66e0be92))
* **get_depth_book:** updated digifinex, deribit APIDepthbook struct to use PriceArray ([a8e2ae5](https://github.com/herenow/atomic-gtw/commit/a8e2ae5ea06d5d9fd24f26b70b35b24084683344))

## [1.18.0](https://github.com/herenow/atomic-gtw/compare/v1.17.1...v1.18.0) (2024-05-13)


### ✨ Features

* coinbase account_gateway, gateway methods update ([453b034](https://github.com/herenow/atomic-gtw/commit/453b034f2cbb9e38ec60bf57c84365dc54771bab))
* coinbase createOrder and cancelOrder methods ([3faf998](https://github.com/herenow/atomic-gtw/commit/3faf9985db397581d0a9ed473dbb2571630f3397))
* coinbase integration update to new api version ([5227173](https://github.com/herenow/atomic-gtw/commit/5227173434a272eb08a65e64f6ebac051fad27ee))


### ♻️ Refactor

* resolve pr comments ([a6f84d9](https://github.com/herenow/atomic-gtw/commit/a6f84d9f6ebd925e4d928868f880fcd84652f206))
* resolved code review comments ([4231a91](https://github.com/herenow/atomic-gtw/commit/4231a91f885287c58e0987b07532d8beb1e695a8))


### 🐛 Bug Fixes

* **coinbase:** Various fixes ([5f04a7f](https://github.com/herenow/atomic-gtw/commit/5f04a7f16b7194d6844ff08eddceaf89ae25bf47))


### 📦 Chores

* **test_integration:** Improve depth print ([1de67a4](https://github.com/herenow/atomic-gtw/commit/1de67a406944cbb2b1c3c77db50239aed152705c))

## [1.17.1](https://github.com/herenow/atomic-gtw/compare/v1.17.0...v1.17.1) (2024-05-03)


### 📦 Chores

* **mercadobitcoin:** Tag api errors ([7f69a9b](https://github.com/herenow/atomic-gtw/commit/7f69a9bfe82d2d6d552803f9140356b1b2902d53))

## [1.17.0](https://github.com/herenow/atomic-gtw/compare/v1.16.1...v1.17.0) (2024-05-03)


### ✨ Features

* **bitypreco:** Fetch available markets from API ([dc42e7d](https://github.com/herenow/atomic-gtw/commit/dc42e7d3d38ba88a33e6eaeca68aae74210ac8ed))

## [1.16.1](https://github.com/herenow/atomic-gtw/compare/v1.16.0...v1.16.1) (2024-05-03)


### 📦 Chores

* **deps:** bump the go_modules group across 1 directory with 2 updates ([75201d0](https://github.com/herenow/atomic-gtw/commit/75201d06fcb75825346919004ae6b186f414ebff))

## [1.16.0](https://github.com/herenow/atomic-gtw/compare/v1.15.1...v1.16.0) (2024-04-29)


### ✨ Features

* update coinbase market data ws handling ([9f8c1a7](https://github.com/herenow/atomic-gtw/commit/9f8c1a79a73eaf547cbe012e855b01c2c13ea631))

## [1.15.1](https://github.com/herenow/atomic-gtw/compare/v1.15.0...v1.15.1) (2024-03-29)


### 📦 Chores

* **get_depth_book:** Missing params and test integration ([8fe3d58](https://github.com/herenow/atomic-gtw/commit/8fe3d589c482f65643985de2cf575184c877c1e1))

## [1.15.0](https://github.com/herenow/atomic-gtw/compare/v1.14.4...v1.15.0) (2024-03-27)


### ✨ Features

* **get_depth_book:** Interface to fetch book via REST ([d9ec140](https://github.com/herenow/atomic-gtw/commit/d9ec14060cc283fddd2b64a1d19c9387711485ee))

## [1.14.4](https://github.com/herenow/atomic-gtw/compare/v1.14.3...v1.14.4) (2024-03-13)


### 📦 Chores

* **deps:** bump google.golang.org/protobuf from 1.25.0 to 1.33.0 ([a8f7b72](https://github.com/herenow/atomic-gtw/commit/a8f7b7263d29796d20b082e1d7f7bf1b5216b3ea))

## [1.14.3](https://github.com/herenow/atomic-gtw/compare/v1.14.2...v1.14.3) (2024-03-13)


### 🐛 Bug Fixes

* **bitso:** Bitso trade direction was inverted ([6acbdda](https://github.com/herenow/atomic-gtw/commit/6acbddac02ca890d5e5f49ff83d38399827ca2c3))

## [1.14.2](https://github.com/herenow/atomic-gtw/compare/v1.14.1...v1.14.2) (2024-03-13)


### 📦 Chores

* **bitso:** Check orderbook msg sequencing ([75eaa5a](https://github.com/herenow/atomic-gtw/commit/75eaa5afac418267c9f8b36ea36a354444c90c4b))

## [1.14.1](https://github.com/herenow/atomic-gtw/compare/v1.14.0...v1.14.1) (2024-03-08)


### 📦 Chores

* **deribit:** Add new currencies ([193871b](https://github.com/herenow/atomic-gtw/commit/193871b4513167e793abf060fc66df1e0cac67b1))

## [1.14.0](https://github.com/herenow/atomic-gtw/compare/v1.13.7...v1.14.0) (2024-03-06)


### ✨ Features

* **bitso:** All all requests optionally ([ac616ed](https://github.com/herenow/atomic-gtw/commit/ac616ed3e31e8cd8294450e68eeaf10a0d5a6c12))
* **bitso:** No proxies http client for private requests ([860c28d](https://github.com/herenow/atomic-gtw/commit/860c28d446564ddff3e78daa512072d6d0db1e82))


### 🐛 Bug Fixes

* **bitso:** getNonce missing mutex ([04e011e](https://github.com/herenow/atomic-gtw/commit/04e011e5bfe8510b374d1a2c3e7c2f42a7efc734))

## [1.13.7](https://github.com/herenow/atomic-gtw/compare/v1.13.6...v1.13.7) (2024-03-04)


### 🐛 Bug Fixes

* **ripio:** Only auth required endpoints ([76512f4](https://github.com/herenow/atomic-gtw/commit/76512f40c070aa7d1ba3dd4ea56dc77441a60ef5))

## [1.13.6](https://github.com/herenow/atomic-gtw/compare/v1.13.5...v1.13.6) (2024-02-08)


### 🐛 Bug Fixes

* **okx:** Fee fill tracking ([2ce9dff](https://github.com/herenow/atomic-gtw/commit/2ce9dff7016d0fa31a26fe90925bcde081238018))

## [1.13.5](https://github.com/herenow/atomic-gtw/compare/v1.13.4...v1.13.5) (2024-02-01)


### 🐛 Bug Fixes

* **mercadobitcoin:** Fee rate percent to ratio ([3110973](https://github.com/herenow/atomic-gtw/commit/311097336445f75c444c278d8a2af7a89cf38111))

## [1.13.4](https://github.com/herenow/atomic-gtw/compare/v1.13.3...v1.13.4) (2024-01-31)


### 📦 Chores

* **types:** Fee and Fee Asset to gateway.Execution ([0234cbc](https://github.com/herenow/atomic-gtw/commit/0234cbcf3c07b99ce922741b6013f7b1b1ef92c8))

## [1.13.3](https://github.com/herenow/atomic-gtw/compare/v1.13.2...v1.13.3) (2024-01-31)


### 📦 Chores

* **onetrading:** New onetrading fast api ([36d2c78](https://github.com/herenow/atomic-gtw/commit/36d2c780bc61b5962aa661aeb38f676e4cb40cae))

## [1.13.2](https://github.com/herenow/atomic-gtw/compare/v1.13.1...v1.13.2) (2024-01-30)


### 🐛 Bug Fixes

* **bybit:** Send order error check ([db0dbb8](https://github.com/herenow/atomic-gtw/commit/db0dbb85f402f2ae379fb0be4c94431e28aad002))

## [1.13.1](https://github.com/herenow/atomic-gtw/compare/v1.13.0...v1.13.1) (2024-01-30)


### 📦 Chores

* **go:** Update go version to latest ([1857dcd](https://github.com/herenow/atomic-gtw/commit/1857dcdae97a32b5f2182b239a27471927a1107f))

## [1.13.0](https://github.com/herenow/atomic-gtw/compare/v1.12.0...v1.13.0) (2024-01-26)


### ✨ Features

* **bitso:** Custom price tick ([b48de96](https://github.com/herenow/atomic-gtw/commit/b48de96914603a866ff0ee57590c96b491980159))

## [1.12.0](https://github.com/herenow/atomic-gtw/compare/v1.11.22...v1.12.0) (2024-01-24)


### ✨ Features

* **codeium:** Codemium  pr-agent for PR code review ([54ad4f4](https://github.com/herenow/atomic-gtw/commit/54ad4f474a4e55e6e19c59482fc0b30139dfbbca))

## [1.11.22](https://github.com/herenow/atomic-gtw/compare/v1.11.21...v1.11.22) (2024-01-24)


### 📦 Chores

* **deps:** bump golang.org/x/net from 0.16.0 to 0.17.0 ([d5dff26](https://github.com/herenow/atomic-gtw/commit/d5dff26d3153557817a0fd846b814a6ff6966811))

## [1.11.21](https://github.com/herenow/atomic-gtw/compare/v1.11.20...v1.11.21) (2024-01-19)


### 🐛 Bug Fixes

* **bitso:** Missing dispatch eventLog and missing locks ([84654d9](https://github.com/herenow/atomic-gtw/commit/84654d901e970fc750e4364e3b256141d8220593))

## [1.11.20](https://github.com/herenow/atomic-gtw/compare/v1.11.19...v1.11.20) (2024-01-19)


### 📦 Chores

* **bitso:** Use the diff-orders channel for market data ([6296560](https://github.com/herenow/atomic-gtw/commit/6296560f280f3b2c0296d8e63b76ce0748aa700d))

## [1.11.19](https://github.com/herenow/atomic-gtw/compare/v1.11.18...v1.11.19) (2024-01-19)


### 📦 Chores

* **book_viewer:** Missing gateway.Options pass ([059df83](https://github.com/herenow/atomic-gtw/commit/059df83969cca296cf5d57894dabff6d5083d3cb))

## [1.11.18](https://github.com/herenow/atomic-gtw/compare/v1.11.17...v1.11.18) (2024-01-17)


### 🐛 Bug Fixes

* **mercadobitcoin:** Order tracking of sent orders ([60e70cf](https://github.com/herenow/atomic-gtw/commit/60e70cf211685b33a242928fc39e72aa38a031e7))

## [1.11.17](https://github.com/herenow/atomic-gtw/compare/v1.11.16...v1.11.17) (2024-01-09)


### 📦 Chores

* **bitypreco:** Add new markets ([8a51312](https://github.com/herenow/atomic-gtw/commit/8a513126ddb9d1cf6b451187dd2adcb4257eb757))

## [1.11.16](https://github.com/herenow/atomic-gtw/compare/v1.11.15...v1.11.16) (2024-01-08)


### 🐛 Bug Fixes

* **lbank:** Available balance was incorrect ([f0bc630](https://github.com/herenow/atomic-gtw/commit/f0bc63086ded08022b72d6c3b4918095a30bb951))

## [1.11.15](https://github.com/herenow/atomic-gtw/compare/v1.11.14...v1.11.15) (2024-01-02)


### 🐛 Bug Fixes

* **currencylayer:** Should only request market data when requested to ([ff1b4eb](https://github.com/herenow/atomic-gtw/commit/ff1b4eb046cb1370cdf7cf34961d497f284f3daa))

## [1.11.14](https://github.com/herenow/atomic-gtw/compare/v1.11.13...v1.11.14) (2023-12-22)


### 📦 Chores

* **onetrading:** Map rate limit errors ([beafb62](https://github.com/herenow/atomic-gtw/commit/beafb62cb176af61cb275f941b2d338deafa2f16))

## [1.11.13](https://github.com/herenow/atomic-gtw/compare/v1.11.12...v1.11.13) (2023-12-18)


### 📦 Chores

* **digitra:** Move balances api to new V3 ([98f2026](https://github.com/herenow/atomic-gtw/commit/98f20264669a802f984a6373ddfbe525dfecd987))

## [1.11.12](https://github.com/herenow/atomic-gtw/compare/v1.11.11...v1.11.12) (2023-12-13)


### 🐛 Bug Fixes

* **deribit:** New USDT currency ([457bec7](https://github.com/herenow/atomic-gtw/commit/457bec77a94f41656b9d9b2c78d658b7b1667e44))

## [1.11.11](https://github.com/herenow/atomic-gtw/compare/v1.11.10...v1.11.11) (2023-12-12)


### 🐛 Bug Fixes

* **hotcoin:** API body post was being sent as query string ([af50710](https://github.com/herenow/atomic-gtw/commit/af50710a8ba88c826adeb80be009ceaf5d11cc33))

## [1.11.10](https://github.com/herenow/atomic-gtw/compare/v1.11.9...v1.11.10) (2023-12-08)


### 🐛 Bug Fixes

* **bitstamp:** Market data gateway and account gateway updates ([1d60286](https://github.com/herenow/atomic-gtw/commit/1d602869d6c7042aefffac06c867cb552b4c84f3))

## [1.11.9](https://github.com/herenow/atomic-gtw/compare/v1.11.8...v1.11.9) (2023-12-07)


### 📦 Chores

* **currencylayer:** Lower default refresh interval ms ([8ada29e](https://github.com/herenow/atomic-gtw/commit/8ada29e0e177f00c3eb5885f63c6dde9bdcad31f))

## [1.11.8](https://github.com/herenow/atomic-gtw/compare/v1.11.7...v1.11.8) (2023-12-04)


### 🐛 Bug Fixes

* **kucoin:** Update insufficient balance error check ([e411de7](https://github.com/herenow/atomic-gtw/commit/e411de7588e8f1c8e32319d8214c7e21753e76d3))

## [1.11.7](https://github.com/herenow/atomic-gtw/compare/v1.11.6...v1.11.7) (2023-11-30)


### 🐛 Bug Fixes

* **ftx:** Ftx typo on instance variable ([0051142](https://github.com/herenow/atomic-gtw/commit/0051142f26feff2629947351ae437f0e6a4dd6f0))

## [1.11.6](https://github.com/herenow/atomic-gtw/compare/v1.11.5...v1.11.6) (2023-11-30)


### 🐛 Bug Fixes

* **fastforex,ftx:** Small fixes ([291b4b1](https://github.com/herenow/atomic-gtw/commit/291b4b1a0c1dbea552743b76164616999070fb7c))


### 📦 Chores

* **bitso:** Allow for rate limit errors when updating initial book ([cc597e2](https://github.com/herenow/atomic-gtw/commit/cc597e2885f15722e81541af58c7f79f5a2a553b))

## [1.11.5](https://github.com/herenow/atomic-gtw/compare/v1.11.4...v1.11.5) (2023-11-28)


### 📦 Chores

* **binance:** Fast and custom ping/pongs ([8de1dab](https://github.com/herenow/atomic-gtw/commit/8de1dabf206851c5d5c0759b22104715dd405665))

## [1.11.4](https://github.com/herenow/atomic-gtw/compare/v1.11.3...v1.11.4) (2023-11-28)


### 📦 Chores

* **latoken:** Fetch user trades ([77f594a](https://github.com/herenow/atomic-gtw/commit/77f594a4d304c7459e38ef2a952249e175d83a60))

## [1.11.3](https://github.com/herenow/atomic-gtw/compare/v1.11.2...v1.11.3) (2023-11-28)


### 🐛 Bug Fixes

* **binance:** New api client baseUrl ([25a953c](https://github.com/herenow/atomic-gtw/commit/25a953c2e650ef999b6b318051bc216a870dd44b))

## [1.11.2](https://github.com/herenow/atomic-gtw/compare/v1.11.1...v1.11.2) (2023-11-22)


### 📦 Chores

* **ripio:** Use new amount_tick ([86e67a6](https://github.com/herenow/atomic-gtw/commit/86e67a6b8738b279358a765f0525143858b95592))
* **ripio:** Use user_trades instead of user_orders ([772a44a](https://github.com/herenow/atomic-gtw/commit/772a44ac69d66b9a8af3cd2636fac585a8abd675))

## [1.11.1](https://github.com/herenow/atomic-gtw/compare/v1.11.0...v1.11.1) (2023-11-21)


### 📦 Chores

* **bybit:** Ping pong scheme ([dc4aece](https://github.com/herenow/atomic-gtw/commit/dc4aece89db90fe0fa4f6bbbae0cb109fac7e56a))

## [1.11.0](https://github.com/herenow/atomic-gtw/compare/v1.10.3...v1.11.0) (2023-11-21)


### 🐛 Bug Fixes

* **onetrading:** Heartbeat timeout ([4c0e61c](https://github.com/herenow/atomic-gtw/commit/4c0e61c8b5b899a0935913d8d13cb0e57504ec65))


### 📦 Chores

* **onetrading:** General improvements on websocket ([28272c1](https://github.com/herenow/atomic-gtw/commit/28272c12b179530aef7938293b0e0b27923c2c67))
* **release:** 1.10.4 [skip ci] ([480303e](https://github.com/herenow/atomic-gtw/commit/480303ef7582427503305c57ba9958e7f47ea99a))
* **release:** 1.10.4 [skip ci] ([5ad886e](https://github.com/herenow/atomic-gtw/commit/5ad886e9641d9dce6cea91b63195ac1a2c89cfd2))


### ✨ Features

* Empty commit to force new release ([710baed](https://github.com/herenow/atomic-gtw/commit/710baed5364519ecbeffbd3bd51248aee3da8b5d))

## [1.10.4](https://github.com/herenow/atomic-gtw/compare/v1.10.3...v1.10.4) (2023-11-21)


### 🐛 Bug Fixes

* **onetrading:** Heartbeat timeout ([4c0e61c](https://github.com/herenow/atomic-gtw/commit/4c0e61c8b5b899a0935913d8d13cb0e57504ec65))


### 📦 Chores

* **onetrading:** General improvements on websocket ([28272c1](https://github.com/herenow/atomic-gtw/commit/28272c12b179530aef7938293b0e0b27923c2c67))
* **release:** 1.10.4 [skip ci] ([5ad886e](https://github.com/herenow/atomic-gtw/commit/5ad886e9641d9dce6cea91b63195ac1a2c89cfd2))

## [1.10.4](https://github.com/herenow/atomic-gtw/compare/v1.10.3...v1.10.4) (2023-11-20)


### 🐛 Bug Fixes

* **onetrading:** Heartbeat timeout ([4c0e61c](https://github.com/herenow/atomic-gtw/commit/4c0e61c8b5b899a0935913d8d13cb0e57504ec65))

## [1.10.3](https://github.com/herenow/atomic-gtw/compare/v1.10.2...v1.10.3) (2023-11-17)


### 🐛 Bug Fixes

* **onetrading:** Trade event tracking ([ac2be7f](https://github.com/herenow/atomic-gtw/commit/ac2be7f5d73725e65a563a70811c5862fc154061))

## [1.10.2](https://github.com/herenow/atomic-gtw/compare/v1.10.1...v1.10.2) (2023-11-17)


### 🐛 Bug Fixes

* **onetrading:** PRICE_TICKS channel name ([f26e382](https://github.com/herenow/atomic-gtw/commit/f26e3820b89a168b0207c5a9f406ce9db131ab2d))

## [1.10.1](https://github.com/herenow/atomic-gtw/compare/v1.10.0...v1.10.1) (2023-11-17)


### 📦 Chores

* **gateway:** Use md5 instead of sha256 for trade.FakeID() ([0731b45](https://github.com/herenow/atomic-gtw/commit/0731b458e3c96ed747095042bdb7b2a54130f799))

## [1.10.0](https://github.com/herenow/atomic-gtw/compare/v1.9.7...v1.10.0) (2023-11-17)


### ✨ Features

* **gateway:** Fake trade id ([a50123d](https://github.com/herenow/atomic-gtw/commit/a50123d810980a4dbb4c29b5f74672459e170391))

## [1.9.7](https://github.com/herenow/atomic-gtw/compare/v1.9.6...v1.9.7) (2023-11-17)


### 🐛 Bug Fixes

* **onetrading:** Market data trades tracking and connect ([18715a4](https://github.com/herenow/atomic-gtw/commit/18715a4629fdc4d9fbad4610e5b2d0ebe27abe5f))

## [1.9.6](https://github.com/herenow/atomic-gtw/compare/v1.9.5...v1.9.6) (2023-11-17)


### 🐛 Bug Fixes

* **bybit:** eventLog slice capacity ([1b30b13](https://github.com/herenow/atomic-gtw/commit/1b30b1364be36a24b3d28374fef2b9c0b293c6be))


### 📦 Chores

* **bybit:** Use mapAPIOrderSideToCommonSide in ws auth trade ([9712665](https://github.com/herenow/atomic-gtw/commit/97126654fab742ab43c4395fcbd98e638adf4b59))

## [1.9.5](https://github.com/herenow/atomic-gtw/compare/v1.9.4...v1.9.5) (2023-11-17)


### 🐛 Bug Fixes

* **bybit:** Public trades tracking ([cfb5245](https://github.com/herenow/atomic-gtw/commit/cfb5245db1fe71418b75fc7f7c2dcecd912fc1d3))

## [1.9.4](https://github.com/herenow/atomic-gtw/compare/v1.9.3...v1.9.4) (2023-11-17)


### 📦 Chores

* **onetrading:** Map known insufficient funds err ([1114e0d](https://github.com/herenow/atomic-gtw/commit/1114e0d96b7b1b996526a5558f5a09d53a93ef83))

## [1.9.3](https://github.com/herenow/atomic-gtw/compare/v1.9.2...v1.9.3) (2023-11-17)


### 🐛 Bug Fixes

* **onetrading:** Order trading fill json struct ([796d608](https://github.com/herenow/atomic-gtw/commit/796d608a4e75a29468da57047cead1fce66f1172))

## [1.9.2](https://github.com/herenow/atomic-gtw/compare/v1.9.1...v1.9.2) (2023-11-16)


### 🐛 Bug Fixes

* **onetrading:** Check http status code response ([4b71581](https://github.com/herenow/atomic-gtw/commit/4b71581b7af8fc1d2fb0ed5b7e6b797493cc043f))
* **onetrading:** Order book updates ([6f7061d](https://github.com/herenow/atomic-gtw/commit/6f7061dda05ae8ee53c237caf967b4741417086a))
* **onetrading:** Snapshot must be the first event ([d99aaad](https://github.com/herenow/atomic-gtw/commit/d99aaad78d67ccba9d6865dfdcdcafac9670183c))
* **onetrading:** Token option ([a014b17](https://github.com/herenow/atomic-gtw/commit/a014b17bb1cb32d5793ce63bd6ed13bc5f32eed2))
* **onetrading:** Ws auth success check ([2229136](https://github.com/herenow/atomic-gtw/commit/2229136225015021962460a449d5455279e5f55c))

## [1.9.1](https://github.com/herenow/atomic-gtw/compare/v1.9.0...v1.9.1) (2023-11-16)


### 🐛 Bug Fixes

* **coinsph:** Fix snapshot order in eventLog ([708187d](https://github.com/herenow/atomic-gtw/commit/708187d2fef09b0475d8581d740219cc2e42ee7e))

## [1.9.0](https://github.com/herenow/atomic-gtw/compare/v1.8.0...v1.9.0) (2023-11-10)


### ✨ Features

* **ripio:** Wait for order entry ([d5c9c2f](https://github.com/herenow/atomic-gtw/commit/d5c9c2fbcd1892757b145ee4995621d0bee7cbbb))

## [1.8.0](https://github.com/herenow/atomic-gtw/compare/v1.7.1...v1.8.0) (2023-11-10)


### ✨ Features

* **ripio:** Map send/cancel order known errors ([9463c0c](https://github.com/herenow/atomic-gtw/commit/9463c0cb53690df6ccea3c73022c7ab048af85c3))

## [1.7.1](https://github.com/herenow/atomic-gtw/compare/v1.7.0...v1.7.1) (2023-11-10)


### 🐛 Bug Fixes

* **coinsph:** Imported twice on mapper ([47f2f47](https://github.com/herenow/atomic-gtw/commit/47f2f478c651cefbe59f06ff45e3789898d7c760))

## [1.7.0](https://github.com/herenow/atomic-gtw/compare/v1.6.0...v1.7.0) (2023-11-10)


### ✨ Features

* **ripio:** New exchagne api ([2a8a254](https://github.com/herenow/atomic-gtw/commit/2a8a254b970cef840d9ef38fe80aefee1b8b891f))

## [1.6.0](https://github.com/herenow/atomic-gtw/compare/v1.5.6...v1.6.0) (2023-11-10)


### ✨ Features

* **onetrading:** authenticated ws trading channel messages ([c39a53e](https://github.com/herenow/atomic-gtw/commit/c39a53ef1f29e0e3633fee56fb9a559e58c071a6))
* **onetrading:** one trading exchange integration, markets, order actions ([f761fc8](https://github.com/herenow/atomic-gtw/commit/f761fc8debcbf081fd5b20495883a527a4853cc1))
* **onetrading:** subscribe markets ws method implementation ([ad07741](https://github.com/herenow/atomic-gtw/commit/ad0774139d17b7710356c43f9f0a7335b7ab3399))


### 🐛 Bug Fixes

* **onetrading:** adjusted state and status mapping, params usage and heartbeatchannel reach ([aca6e26](https://github.com/herenow/atomic-gtw/commit/aca6e2629fb410a0279bb2eddc85eca84d6102d3))
* **onetrading:** fill event usage instead ([4f8704c](https://github.com/herenow/atomic-gtw/commit/4f8704c22070a2d861e5705fb52fc87627686897))
* **onetrading:** removed unsued variables + channel usage fix + some method refactors ([11c1696](https://github.com/herenow/atomic-gtw/commit/11c16968dbce074527e84ca3c6b73c09194e9048))
* **onetrading:** struct naming adjustment, moved header setter to request func ([66b92d9](https://github.com/herenow/atomic-gtw/commit/66b92d9ac63ed251a15355e22e650c335d01ae44))

## [1.5.6](https://github.com/herenow/atomic-gtw/compare/v1.5.5...v1.5.6) (2023-11-08)


### 🐛 Bug Fixes

* **safegateway:** Fixes and reconnect procedure fix ([34b03e0](https://github.com/herenow/atomic-gtw/commit/34b03e07f1e52b16255e964da4ff0d20c47b3865))

## [1.5.5](https://github.com/herenow/atomic-gtw/compare/v1.5.4...v1.5.5) (2023-11-07)


### 🐛 Bug Fixes

* **safegateway:** Remove logging from tick processor ([70caa02](https://github.com/herenow/atomic-gtw/commit/70caa022b79527bc91e8005acd6496960deb6a88))

## [1.5.4](https://github.com/herenow/atomic-gtw/compare/v1.5.3...v1.5.4) (2023-11-07)


### 🐛 Bug Fixes

* **safegateway:** Infinite loop when restarting ([b684053](https://github.com/herenow/atomic-gtw/commit/b68405351d69de459d32b5b6bb905bbb11a3ca15))

## [1.5.3](https://github.com/herenow/atomic-gtw/compare/v1.5.2...v1.5.3) (2023-11-07)


### 🐛 Bug Fixes

* **safegateway:** Missing Exchange() method ([c3a8c21](https://github.com/herenow/atomic-gtw/commit/c3a8c211c3629b77ff31b32bfe148a69e33a1d61))

## [1.5.2](https://github.com/herenow/atomic-gtw/compare/v1.5.1...v1.5.2) (2023-11-07)


### 🐛 Bug Fixes

* **safegateway:** Bin path env ([401d70b](https://github.com/herenow/atomic-gtw/commit/401d70b8872339acac21193c2d319364a92747e5))

## [1.5.1](https://github.com/herenow/atomic-gtw/compare/v1.5.0...v1.5.1) (2023-11-07)


### 📦 Chores

* **safegateway:** Improve reconnect if transport dies ([3fe4381](https://github.com/herenow/atomic-gtw/commit/3fe4381f7126fbfe62c2f3107916fe868aa57e68))

## [1.5.0](https://github.com/herenow/atomic-gtw/compare/v1.4.8...v1.5.0) (2023-11-04)


### ✨ Features

* **safe_gateway:** Gateway wrapper for safe usage ([f47b77c](https://github.com/herenow/atomic-gtw/commit/f47b77c3ff2c28d5352c46a63d68814e49f92d68))

## [1.4.8](https://github.com/herenow/atomic-gtw/compare/v1.4.7...v1.4.8) (2023-11-03)


### 🐛 Bug Fixes

* **proxy_tester:** Fallback url update ([f3f0537](https://github.com/herenow/atomic-gtw/commit/f3f05371163c4ef918133da838bddf7e159ab72a))

## [1.4.7](https://github.com/herenow/atomic-gtw/compare/v1.4.6...v1.4.7) (2023-11-02)


### 📦 Chores

* **proxy_tester:** Fallback url test ([c24c055](https://github.com/herenow/atomic-gtw/commit/c24c055559c25a8e3b2637a65c7e329c49c0f612))

## [1.4.6](https://github.com/herenow/atomic-gtw/compare/v1.4.5...v1.4.6) (2023-11-01)


### 📦 Chores

* **bybit:** Missing send order and cancel order err mappings ([badbf6c](https://github.com/herenow/atomic-gtw/commit/badbf6cfeafef2e1633631b5ac0008eeb8f6e3cf))

## [1.4.5](https://github.com/herenow/atomic-gtw/compare/v1.4.4...v1.4.5) (2023-11-01)


### 🐛 Bug Fixes

* **bybit:** Signature was using incorrect timestamp ([1bad198](https://github.com/herenow/atomic-gtw/commit/1bad1984a5efc0d11de749cfac65173b239d612d))

## [1.4.4](https://github.com/herenow/atomic-gtw/compare/v1.4.3...v1.4.4) (2023-11-01)


### 🐛 Bug Fixes

* **azbit,bitget,bybit:** New http request body ([f6fe6fc](https://github.com/herenow/atomic-gtw/commit/f6fe6fc893f1b7f459b8d4fc53e85908fb6c9a87))

## [1.4.3](https://github.com/herenow/atomic-gtw/compare/v1.4.2...v1.4.3) (2023-11-01)


### ♻️ Refactor

* removed loadMarkets from coinsph to use GetMarkets instead ([bc5757e](https://github.com/herenow/atomic-gtw/commit/bc5757e10f4426df9699892a4316470a80651218))

## [1.4.2](https://github.com/herenow/atomic-gtw/compare/v1.4.1...v1.4.2) (2023-10-31)


### 🐛 Bug Fixes

* **bybit:** Balances attributes ([97c4534](https://github.com/herenow/atomic-gtw/commit/97c45340c94f92372b38431d6e3611d586f229b5))

## [1.4.1](https://github.com/herenow/atomic-gtw/compare/v1.4.0...v1.4.1) (2023-10-30)


### 🐛 Bug Fixes

* **bybit:** Fix accountType param on balances req ([11a24d6](https://github.com/herenow/atomic-gtw/commit/11a24d6d79f6f55dfde215865d0069aa5543121b))

## [1.4.0](https://github.com/herenow/atomic-gtw/compare/v1.3.27...v1.4.0) (2023-10-27)


### ✨ Features

* coinsph integratation pong resp + place order + cancel order + user stream data ([9108162](https://github.com/herenow/atomic-gtw/commit/910816291e3da01dde09954f50ce25ddaceb5175))
* coinsph integratation requested adjustments + pingpong handle ([a13f3a1](https://github.com/herenow/atomic-gtw/commit/a13f3a1d3885073b2bea903cb752400bdb28dd56))
* coinsph integration get balance + open orders ([613121c](https://github.com/herenow/atomic-gtw/commit/613121c697ef95c82ea8a0b3e84c6ce813cae73e))
* coinsph integration load markets ([6ad1c19](https://github.com/herenow/atomic-gtw/commit/6ad1c1981dc0dce2943dd317c26fc4005c922a0a))
* coinsph integration load markets ([2a7d35f](https://github.com/herenow/atomic-gtw/commit/2a7d35f2cf3d29a62f50819796e02b327dddd765))
* coinsph integration ws market data subscribe markets ([3fd1dc9](https://github.com/herenow/atomic-gtw/commit/3fd1dc953c20a1f887998f119f88d889db41559b))


### 🐛 Bug Fixes

* coinsph integration - fixes on ws + bids/asks + error printings ([4623dc5](https://github.com/herenow/atomic-gtw/commit/4623dc5547dc7eea0ab71f7d14f83adfda466d40))

## [1.3.27](https://github.com/herenow/atomic-gtw/compare/v1.3.26...v1.3.27) (2023-10-27)


### 🐛 Bug Fixes

* **novadax:** Book updates from websocket ([9465199](https://github.com/herenow/atomic-gtw/commit/94651991aa453291f3cb77a9163c7fe4ef726f02))

## [1.3.26](https://github.com/herenow/atomic-gtw/compare/v1.3.25...v1.3.26) (2023-10-25)


### 🐛 Bug Fixes

* **latoken:** GetMarkets and load markets fixes ([04291f6](https://github.com/herenow/atomic-gtw/commit/04291f61c13dbedba0e2de247074981e43252664))

## [1.3.25](https://github.com/herenow/atomic-gtw/compare/v1.3.24...v1.3.25) (2023-10-25)


### 🐛 Bug Fixes

* **bit:** Fix symbols loading for marketsMap ([6713356](https://github.com/herenow/atomic-gtw/commit/6713356b50dc44a979f130bbaf68cbc034ae56da))


### ♻️ Refactor

* adjusted spacing on some methods, removed unused g.market and g.marketsMap from structs ([3dbe838](https://github.com/herenow/atomic-gtw/commit/3dbe83841cbe773340fa970738b3eafaf8d58b1a))
* latoken GetMarkets update ([4751ffc](https://github.com/herenow/atomic-gtw/commit/4751ffc682d1a5c7df9bdaf7edd5cffd3b17b67a))
* removed g.market and g.marketsMap usage on GetMarkets method on most integrations. ([57a0fa9](https://github.com/herenow/atomic-gtw/commit/57a0fa9a54a3d2671c82a00b182ce488752984d0))
* removed unnecessary variable usage on NewGateway method ([dafc328](https://github.com/herenow/atomic-gtw/commit/dafc32834ceb3d885a5ee35af6c8cce458425017))
* resolved comment change suggestions ([d54154a](https://github.com/herenow/atomic-gtw/commit/d54154ab00159e1747d8e64b2880c1cfef14a2a8))
* updated markets method to use getMarkets instead ([fa28ed0](https://github.com/herenow/atomic-gtw/commit/fa28ed0b7b82208aed1da56e66fe9a6926aafda1))
* updated symbosMap usage ([9f6bb2d](https://github.com/herenow/atomic-gtw/commit/9f6bb2d0213ec6fd56dccd9bcbd073af253f488f))

## [1.3.24](https://github.com/herenow/atomic-gtw/compare/v1.3.23...v1.3.24) (2023-10-20)


### 📦 Chores

* **xt:** Refactor GetMarkets() ([993b4e1](https://github.com/herenow/atomic-gtw/commit/993b4e1f76eed28d3dbf44edec00795e9deab74b))

## [1.3.23](https://github.com/herenow/atomic-gtw/compare/v1.3.22...v1.3.23) (2023-10-20)


### 📦 Chores

* **binance:** Implement new GetMarkets() method ([d442353](https://github.com/herenow/atomic-gtw/commit/d442353cfc16c6cc957d28ecdf293837ec4caada))

## [1.3.22](https://github.com/herenow/atomic-gtw/compare/v1.3.21...v1.3.22) (2023-10-04)


### 🐛 Bug Fixes

* **latoken:** Load markets ([9558ce6](https://github.com/herenow/atomic-gtw/commit/9558ce6b85ed3539f2d1e41ba29916f05e433dcf))

## [1.3.21](https://github.com/herenow/atomic-gtw/compare/v1.3.20...v1.3.21) (2023-09-14)


### 📦 Chores

* **mercadobitcoin:** Improve API V4 list my orders ([89c37c5](https://github.com/herenow/atomic-gtw/commit/89c37c5138074795dcef3740fa785ea2dda20420))

## [1.3.20](https://github.com/herenow/atomic-gtw/compare/v1.3.19...v1.3.20) (2023-09-12)


### 📦 Chores

* **bitso:** Improve market data side mapping ([57068fd](https://github.com/herenow/atomic-gtw/commit/57068fda94098ecb0753c89101496990ce9cda62))

## [1.3.19](https://github.com/herenow/atomic-gtw/compare/v1.3.18...v1.3.19) (2023-09-12)


### 🐛 Bug Fixes

* **bitso:** Fix order side normalization ([368b32d](https://github.com/herenow/atomic-gtw/commit/368b32de46bbfffec1ceb15c8bc944db6dd26065))

## [1.3.18](https://github.com/herenow/atomic-gtw/compare/v1.3.17...v1.3.18) (2023-09-12)


### 📦 Chores

* **bitso:** Always authenticate when ApiKey present ([1d67ca0](https://github.com/herenow/atomic-gtw/commit/1d67ca0564709673d18753c5a9c32533b1dca34d))

## [1.3.17](https://github.com/herenow/atomic-gtw/compare/v1.3.16...v1.3.17) (2023-09-08)


### 🐛 Bug Fixes

* **bitso:** Fix snapshot fetch sides ([42877ce](https://github.com/herenow/atomic-gtw/commit/42877ce3520bfb4696afcfa912dfefaee97fe4b5))

## [1.3.16](https://github.com/herenow/atomic-gtw/compare/v1.3.15...v1.3.16) (2023-09-08)


### 📦 Chores

* **bitso:** Get initial snapshot orderbook ([a92f1ac](https://github.com/herenow/atomic-gtw/commit/a92f1ac53e4ec9db231f5dad714d5def8e8981d3))

## [1.3.15](https://github.com/herenow/atomic-gtw/compare/v1.3.14...v1.3.15) (2023-08-11)


### 📦 Chores

* **binance:** Map trade timestamp ([f99837b](https://github.com/herenow/atomic-gtw/commit/f99837b67f99a355fa80041558267ae1dc534a4c))

## [1.3.14](https://github.com/herenow/atomic-gtw/compare/v1.3.13...v1.3.14) (2023-08-02)


### 📦 Chores

* **kucoin:** Cleanup signer ([e1c9893](https://github.com/herenow/atomic-gtw/commit/e1c9893cb47f29672f18a8f4cf1918ac2da7c1f5))

## [1.3.13](https://github.com/herenow/atomic-gtw/compare/v1.3.12...v1.3.13) (2023-07-31)


### 🐛 Bug Fixes

* **mercadobitcoin:** WebSocket market data symbol ([8f26271](https://github.com/herenow/atomic-gtw/commit/8f262715accc2e80e0fab0c31d9be7cc4a4a0b4a))

## [1.3.12](https://github.com/herenow/atomic-gtw/compare/v1.3.11...v1.3.12) (2023-07-27)


### 🐛 Bug Fixes

* **currencylayer:** Deadlock when dispatching initial order updates ([1a01650](https://github.com/herenow/atomic-gtw/commit/1a016509e5837616c6b1b1dc7bf74dffef9eb7dd))

## [1.3.11](https://github.com/herenow/atomic-gtw/compare/v1.3.10...v1.3.11) (2023-07-25)


### 🐛 Bug Fixes

* **okx:** Request body buffer being emptied ([2af6bf9](https://github.com/herenow/atomic-gtw/commit/2af6bf978f976df80d7fc60d0e50fde3abe3248b))

## [1.3.10](https://github.com/herenow/atomic-gtw/compare/v1.3.9...v1.3.10) (2023-07-25)


### 🐛 Bug Fixes

* **okx:** Fill details parsing ([17cff06](https://github.com/herenow/atomic-gtw/commit/17cff06765826d08e83ae354bc8fae7220e6c6bd))

## [1.3.9](https://github.com/herenow/atomic-gtw/compare/v1.3.8...v1.3.9) (2023-07-25)


### 📦 Chores

* **okx:** Map not opened order error ([b81da34](https://github.com/herenow/atomic-gtw/commit/b81da3424667841cb787392dc1a9f58e51b91f4b))


### 🐛 Bug Fixes

* **okx:** Order tracking was incorrect ([fb6382f](https://github.com/herenow/atomic-gtw/commit/fb6382f9d04c36ea409d837403fbc48e95d2ba4d))

## [1.3.8](https://github.com/herenow/atomic-gtw/compare/v1.3.7...v1.3.8) (2023-07-25)


### 📦 Chores

* **okx:** Map missing trade time into gateway.Fill ([481197a](https://github.com/herenow/atomic-gtw/commit/481197a3f7e2427dee2a58248db2403fbdc099e8))

## [1.3.7](https://github.com/herenow/atomic-gtw/compare/v1.3.6...v1.3.7) (2023-07-25)


### 📦 Chores

* Remove unecessary logs ([149208f](https://github.com/herenow/atomic-gtw/commit/149208ffcf97c7053622da28983e05f74653de75))

## [1.3.6](https://github.com/herenow/atomic-gtw/compare/v1.3.5...v1.3.6) (2023-07-24)


### 📦 Chores

* **okx:** Custom endpoints for AWS ([f14fc58](https://github.com/herenow/atomic-gtw/commit/f14fc58825907f8cde14b169afd2868365954f20))

## [1.3.5](https://github.com/herenow/atomic-gtw/compare/v1.3.4...v1.3.5) (2023-07-24)


### 📦 Chores

* **okx:** Allow broker ID to be set ([3c08023](https://github.com/herenow/atomic-gtw/commit/3c080231d3202345b07c735c304b5b4d98b7f408))

## [1.3.4](https://github.com/herenow/atomic-gtw/compare/v1.3.3...v1.3.4) (2023-07-22)


### 📦 Chores

* **bitypreco:** Default newestTradeTime to tradesFromTime ([e4e8242](https://github.com/herenow/atomic-gtw/commit/e4e824223dba5c09f04caab8dc08fd0a64b9cfdf))

## [1.3.3](https://github.com/herenow/atomic-gtw/compare/v1.3.2...v1.3.3) (2023-07-21)


### 🐛 Bug Fixes

* **ordertracking:** Fix order tracking routine ([69bac3b](https://github.com/herenow/atomic-gtw/commit/69bac3b3a820c39dc02fa1a53bcf25ae697627dd))

## [1.3.2](https://github.com/herenow/atomic-gtw/compare/v1.3.1...v1.3.2) (2023-07-21)


### 📦 Chores

* **mercadobitcoin:** Remove mtd trades polling rate limiting ([87b987a](https://github.com/herenow/atomic-gtw/commit/87b987a45a4976f16d031053438bb8713a79b048))

## [1.3.1](https://github.com/herenow/atomic-gtw/compare/v1.3.0...v1.3.1) (2023-07-21)


### 📦 Chores

* **mercadobitcoin:** Add Cancel Order endpoint with unit tests ([760d8b6](https://github.com/herenow/atomic-gtw/commit/760d8b609b48e8c1301678b44c2c32ee0baab971))
* **mercadobitcoin:** Add Place Order endpoint with unit tests ([c6bd956](https://github.com/herenow/atomic-gtw/commit/c6bd956475e8af062fd7d045efdf7f401dae7281))
* **mercadobitcoin:** Improve authorize endpoint ([cf89117](https://github.com/herenow/atomic-gtw/commit/cf89117629afc064b0808d7340eee209c235d372))
* **mercadobitcoin:** Improve Orderbook and Trades endpoints ([65455cc](https://github.com/herenow/atomic-gtw/commit/65455ccebf38d15a963fc5580f2b72aa6aed721e))
* **mercadobitcoin:** Improve orders related and account endpoints ([2bda321](https://github.com/herenow/atomic-gtw/commit/2bda3217732e8fb5a385eb20ee20872d26d594a2))
* **mercadobitcoin:** Improve Symbols endpoint ([a637a19](https://github.com/herenow/atomic-gtw/commit/a637a19b5ed4b50e89cbd3e7b7fe0691c5ac77e3))
* **mercadobitcoin:** Remove duplicated code on update order status ([a723341](https://github.com/herenow/atomic-gtw/commit/a723341f27a2030cf40c4a8dc0b3848656588fa4))
* **mercadobitcoin:** Remove legacy API and add get balances endpoint ([2012c8b](https://github.com/herenow/atomic-gtw/commit/2012c8b8b630f80fbcf1ab51df84c13329f82a1d))
* **mercadobitcoin:** Revert side logic and float64 types ([aa18189](https://github.com/herenow/atomic-gtw/commit/aa18189cf719591d10216121c33e8aa0a6d7f59a))

## [1.3.0](https://github.com/herenow/atomic-gtw/compare/v1.2.2...v1.3.0) (2023-07-21)


### ✨ Features

* **bitypreco:** Track market trades events ([c2af977](https://github.com/herenow/atomic-gtw/commit/c2af977383a906cdb4df11ee9fcbb3d700b8b596)), closes [#69](https://github.com/herenow/atomic-gtw/issues/69)

## [1.2.2](https://github.com/herenow/atomic-gtw/compare/v1.2.1...v1.2.2) (2023-07-19)


### 📦 Chores

* **digitra:** List "submitting" orders as open ([23adba6](https://github.com/herenow/atomic-gtw/commit/23adba6555b43457d04c20568fa3aae42b837edf))

## [1.2.1](https://github.com/herenow/atomic-gtw/compare/v1.2.0...v1.2.1) (2023-07-19)


### 🐛 Bug Fixes

* **mercadobitcoin:** Since tid parameter not working ([cc4a59a](https://github.com/herenow/atomic-gtw/commit/cc4a59a9ca3ef30841eb570a83517ebec8268f32))

## [1.2.0](https://github.com/herenow/atomic-gtw/compare/v1.1.2...v1.2.0) (2023-07-18)


### ✨ Features

* **digitra:** Track market trades ([18210d4](https://github.com/herenow/atomic-gtw/commit/18210d4e8b29d9b734952a7068806890cd330613))

## [1.1.2](https://github.com/herenow/atomic-gtw/compare/v1.1.1...v1.1.2) (2023-07-18)


### 🐛 Bug Fixes

* rename releaserc file with incorrect name ([b8f4e5a](https://github.com/herenow/atomic-gtw/commit/b8f4e5a1e38e8f230674b2bda711bd8fc18ba2bc))


### ♻️ Refactor

* **binance:** Add cancel order endpoint to internal library ([730e489](https://github.com/herenow/atomic-gtw/commit/730e48976920c1868fe4ef3ac6529649a6971636))
* **binance:** Add depth and futures depth endpoint to internal library ([31d7214](https://github.com/herenow/atomic-gtw/commit/31d721437608059bbe739996fd8f7714cbffb04c))
* **binance:** add new cancel order api to account gateway ([b44bfbf](https://github.com/herenow/atomic-gtw/commit/b44bfbf1b97a7f00e244e14627c8addce0f90cc8))
* **binance:** Add open orders and account balances to internal library ([f422edc](https://github.com/herenow/atomic-gtw/commit/f422edcd572f20d428551e90ca2cdfeccf382269))
* **binance:** Add place order endpoint ([4fff9d2](https://github.com/herenow/atomic-gtw/commit/4fff9d2e257865bb6c2fd783efb3d7bf26c4b43a))
* **binance:** Add symbols endpoint to internal library with unit tests ([bafdd2c](https://github.com/herenow/atomic-gtw/commit/bafdd2c0913ea3cedfb33ed127d4aee8d27ede6c))
* **binance:** add user stream service to local library ([3038eaa](https://github.com/herenow/atomic-gtw/commit/3038eaac4feb69089be1dd2948cb92d948a1b00b))
* **binance:** Fix incorrect side type and add limit maker case ([a202991](https://github.com/herenow/atomic-gtw/commit/a202991fef16e70e17302a3f095c35393fd00242))
* **binance:** remove baseURL parameter from new HTTP request method ([74253ef](https://github.com/herenow/atomic-gtw/commit/74253ef9719bb838fff2a24e620a710d3b737249))
* **binance:** Remove external dependencies and improve code ([2cbe867](https://github.com/herenow/atomic-gtw/commit/2cbe86730450b3d6747827a41857f08170e642e7))


### 📦 Chores

* **book_viewer:** Allow for custom wait time before start ([a441634](https://github.com/herenow/atomic-gtw/commit/a44163439d429158ed2cdb38d9a8c21267ca3133))
