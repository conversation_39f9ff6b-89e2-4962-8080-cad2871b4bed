package safegateway

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/exec"
	"syscall"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/safegateway/service"
	"github.com/vmihailenco/msgpack/v5"
	"go.nanomsg.org/mangos/v3"
	"go.nanomsg.org/mangos/v3/protocol/pair"
)

type SafeGatewayState string

const (
	SafeGatewayStateConnected    SafeGatewayState = "connected"
	SafeGatewayStateConnecting   SafeGatewayState = "connecting"
	SafeGatewayStateDisconnected SafeGatewayState = "disconnected"
)

type SafeGatewayOptions struct {
	OnDisconnect func(error)
	OnConnect    func()
}

type SafeGateway struct {
	base.Gateway
	exchange       gateway.Exchange
	options        gateway.Options
	accountGateway gateway.AccountGateway
	safeOptions    SafeGatewayOptions
	sockFile       string
	sockAddr       string
	sock           mangos.Socket
	transport      service.SafeGatewayTransport
	cmd            *exec.Cmd
	state          SafeGatewayState
	tickCh         chan gateway.Tick
	closing        bool
	ready          bool
	aliveTick      chan struct{}
}

func NewSafeGateway(
	exchange gateway.Exchange,
	options gateway.Options,
	safeOptions SafeGatewayOptions,
) gateway.Gateway {
	gtw := &SafeGateway{
		exchange:    exchange,
		options:     options,
		safeOptions: safeOptions,
		state:       SafeGatewayStateDisconnected,
		tickCh:      make(chan gateway.Tick, 10),
		aliveTick:   make(chan struct{}),
	}
	gtw.accountGateway = NewAccountGateway(gtw)
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (s *SafeGateway) Connect() error {
	if s.state == SafeGatewayStateConnected {
		return nil
	}

	if s.state == SafeGatewayStateConnecting {
		return errors.New("gateway is connecting")
	}

	s.state = SafeGatewayStateConnecting

	// Start an RPC server, for the child process to connect to
	s.sockFile = s.randSockFile()
	s.sockAddr = fmt.Sprintf("ipc://%s", s.sockFile)
	sock, err := pair.NewSocket()
	if err != nil {
		return fmt.Errorf("new socket: %w", err)
	}

	s.sock = sock
	err = s.sock.Listen(s.sockAddr)
	if err != nil {
		return fmt.Errorf("socket listen: %w", err)
	}

	s.transport = service.NewSafeGatewayTransport(sock)
	s.transport.OnMessage(func(msg service.Message) {
		if err := s.processMessage(msg); err != nil {
			log.Printf("%s process message err: %s", s.exchange, err)
		}
	})
	s.transport.OnDisconnect(func(err error) {
		if !s.closing {
			// Transport read errors are expected, if we are closing the socket
			s.fatalErr(err)
		}
	})

	if err = s.transport.Init(); err != nil {
		return fmt.Errorf("init transport: %w", err)
	}

	log.Printf("Safe gateway transport listening on %s", s.sockAddr)

	// Spawn the child process, that will execute the gtw code
	if err = s.startCmd(); err != nil {
		return err
	}

	s.state = SafeGatewayStateConnected
	if s.safeOptions.OnConnect != nil {
		s.safeOptions.OnConnect()
	}

	return nil
}

func (s *SafeGateway) fatalErr(err error) {
	if s.closing {
		// Ignore, we are closing
		log.Printf("Safe gateway received fatal error, but we are closing: %s", err)
		return
	}

	if closeErr := s.Close(); closeErr != nil {
		panic(fmt.Errorf("fatal: %w; close: %w", err, closeErr))
	}

	if s.safeOptions.OnDisconnect != nil {
		s.safeOptions.OnDisconnect(err)
	}
}

func (s *SafeGateway) processMessage(msg service.Message) error {
	switch msg.Method {
	case service.ALIVE:
		select {
		case s.aliveTick <- struct{}{}:
		default:
		}
	case service.GTW_TICK:
		if err := s.processTick(msg); err != nil {
			return fmt.Errorf("process tick: %w", err)
		}
	}

	return nil
}

func (s *SafeGateway) startCmd() error {
	s.state = SafeGatewayStateConnecting

	opts := service.Options{
		Exchange: s.exchange.ID(),
		RPCAddr:  s.sockAddr,
		GtwOpts:  s.options,
	}
	safeOptsStr, err := json.Marshal(opts)
	if err != nil {
		return fmt.Errorf("marshal options: %w", err)
	}

	bin := s.serviceBinPath()
	errBuffer := NewLimitedBuffer(50 * 1024) // 50kb
	cmd := exec.Command(bin, string(safeOptsStr))
	s.cmd = cmd
	s.cmd.Stderr = errBuffer
	err = s.cmd.Start()
	if err != nil {
		return fmt.Errorf("start cmd \"%s\" err: %w", s.cmd, err)
	}
	go s.waitCmd(errBuffer)

	// Wait for the child process to connect to the RPC server
	s.ready = false
	select {
	case <-time.After(5 * time.Second):
		return errors.New("timeout waiting for child process to connect")
	case <-s.aliveTick:
		s.ready = true
	}

	log.Printf("Safe gateway instance connected to %s", s.sockAddr)

	return nil

}

func (s *SafeGateway) waitCmd(errBuf *LimitedBuffer) {
	err := s.cmd.Wait()
	s.state = SafeGatewayStateDisconnected
	if !s.closing {
		err = fmt.Errorf("instance exit: %w", err)
		log.Printf("Safe gateway instance exited with err\nCmd:\n%s\nErr:\n%s\nStdErr:\n%s", s.cmd, err, errBuf.String())
		s.fatalErr(err)
	}
}

func (s *SafeGateway) killCmd() error {
	if s.cmd.ProcessState == nil || s.cmd.ProcessState.Exited() {
		return nil
	}

	err := syscall.Kill(-s.cmd.Process.Pid, syscall.SIGKILL)
	if err != nil {
		return fmt.Errorf("syscall kill: %w", err)
	}

	return nil
}

func (s *SafeGateway) serviceBinPath() string {
	binPath := os.Getenv("SAFEGATEWAY_BIN_PATH")
	if binPath == "" {
		return "./bin/safegateway"
	}
	return binPath
}

func (s *SafeGateway) randFileID() string {
	ts := time.Now().UnixMilli()
	from := 10000000000
	to := 99999999999
	rand := from + rand.Intn(to-from)
	return fmt.Sprintf("%d-%s-%x", ts, s.exchange.ID(), rand)
}

func (s *SafeGateway) randSockFile() string {
	return fmt.Sprintf("tmp/socks/%s", s.randFileID())
}

func (s *SafeGateway) Close() error {
	s.closing = true
	if err := s.cleanup(); err != nil {
		return fmt.Errorf("cleanup: %w", err)
	}
	return nil
}

func (s *SafeGateway) cleanup() error {
	cleanupErrs := make([]error, 0)

	if s.cmd != nil {
		if err := s.killCmd(); err != nil {
			cleanupErrs = append(cleanupErrs, fmt.Errorf("kill cmd: %w", err))
		}
	}

	if s.sock != nil {
		if err := os.Remove(s.sockFile); err != nil {
			cleanupErrs = append(cleanupErrs, fmt.Errorf("remove sock file: %w", err))
		}

		if err := s.sock.Close(); err != nil {
			cleanupErrs = append(cleanupErrs, fmt.Errorf("close sock: %w", err))
		}
	}

	if len(cleanupErrs) > 0 {
		return errors.Join(cleanupErrs...)
	}

	return nil
}

func (s *SafeGateway) processTick(msg service.Message) error {
	var tick gateway.Tick
	err := msgpack.Unmarshal(msg.Data, &tick)
	if err != nil {
		return fmt.Errorf("unmarshal tick: %w", err)
	}

	s.tickCh <- tick
	return nil
}

func (s *SafeGateway) requestService(method service.ServiceMethod, data []byte, resObj interface{}, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	msg, err := s.transport.SendRequest(ctx, method, data)
	if err != nil {
		return err
	}

	if msg.Err != nil {
		return msg.Err
	}

	if resObj != nil {
		err = msgpack.Unmarshal(msg.Data, resObj)
		if err != nil {
			return fmt.Errorf("unmarshal res obj: %w", err)
		}
	}

	return nil
}

func (s *SafeGateway) GetMarkets() ([]gateway.Market, error) {
	var markets []gateway.Market
	err := s.requestService(service.GTW_GET_MARKETS, nil, &markets, 5*time.Second)
	return markets, err
}

func (s *SafeGateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var depthBook gateway.DepthBook

	request := service.GetDepthBookRequest{
		Market: market,
		Params: params,
	}
	data, err := msgpack.Marshal(request)
	if err != nil {
		return depthBook, fmt.Errorf("marshal request: %w", err)
	}

	err = s.requestService(service.GTW_GET_DEPTH_BOOK, data, &depthBook, 5*time.Second)
	return depthBook, err

}

func (s *SafeGateway) SubscribeMarkets(markets []gateway.Market) error {
	data, err := msgpack.Marshal(markets)
	if err != nil {
		return fmt.Errorf("marshal markets: %w", err)
	}
	return s.requestService(service.GTW_SUBSCRIBE_MARKETS, data, nil, 30*time.Second)
}

func (g *SafeGateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (s *SafeGateway) Tick() chan gateway.Tick {
	return s.tickCh
}

func (s *SafeGateway) Exchange() gateway.Exchange {
	return s.exchange
}
