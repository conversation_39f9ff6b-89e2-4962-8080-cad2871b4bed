package bitypreco

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"path"
	"strconv"
	"strings"
	"time"

	"github.com/buger/jsonparser"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase = "https://api.bitpreco.com"

	// private endpoints
	private        = true
	apiBalance     = "/v1/trading/balance"
	apiOpenOrders  = "/v1/trading/open_orders"
	apiPlaceOrder  = "/v1/trading/%s"
	apiCancelOrder = "/v1/trading/order_cancel"
	apiOrderStatus = "/v1/trading/order_status"

	// public endpoints
	public     = false
	apiMarkets = "/v1/get_markets"
	apiDepth   = "/%s/orderbook"
	apiTrades  = "/%s/trades"
)

type API struct {
	options gateway.Options
	client  *utils.HttpClient
	baseURL string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options: options,
		client:  client,
		baseURL: baseURL,
	}
}

type APIMarket struct {
	Base             string  `json:"base"`
	Quote            string  `json:"quote"`
	MinBaseAmount    float64 `json:"min_base_amount"`
	MaxBaseAmount    float64 `json:"max_base_amount"`
	MinQuoteAmount   float64 `json:"min_quote_amount"`
	MaxQuoteAmount   float64 `json:"max_quote_amount"`
	BasePrecision    int     `json:"base_precision"`
	QuotePrecision   int     `json:"quote_precision"`
	IsMarketPlace    bool    `json:"is_marketplace"`
	RFQDisable       bool    `json:"rfq_disable"`
	RFQVisible       bool    `json:"rfq_visible"`
	OrderbookDisable bool    `json:"orderbook_disable"`
	OrderbookVisible bool    `json:"orderbook_visible"`
}

func (a *API) Markets() (res map[string]APIMarket, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiMarkets, nil, public)
	if err != nil {
		return res, fmt.Errorf("failed to request api [%s], error: %s", apiMarkets, err)
	}

	// This is a workaround to avoid the error "403 Forbidden Access"
	req.Header.Set("Origin", "https://market.bitypreco.com/")
	req.Header.Set("Referer", "https://market.bitypreco.com/")

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return res, fmt.Errorf("failed to make request api [%s], error: %s", apiMarkets, err)
	}

	vals := make(map[string]json.RawMessage)
	err = json.Unmarshal(httpRes, &vals)
	if err != nil {
		return res, fmt.Errorf("failed to unmarshal body api [%s], error: %s", apiMarkets, err)
	}

	// This endpoint returns a map of markets, but we must exclude the "success" field, since it's a bool
	res = make(map[string]APIMarket)
	for key, val := range vals {
		if key != "success" {
			var market APIMarket
			err = json.Unmarshal(val, &market)
			if err != nil {
				return res, fmt.Errorf("failed to unmarshal market [%s] api [%s], error: %s", key, apiMarkets, err)
			}

			res[key] = market
		}
	}

	return res, err
}

type APIBalance struct {
	Asset     string
	Available float64
	Freeze    float64
}

func balancesToCommon(b []byte) ([]APIBalance, error) {
	var res map[string]interface{}

	if err := json.Unmarshal(b, &res); err != nil {
		return []APIBalance{}, err
	}

	balances := make([]APIBalance, 0, len(res))
	err := jsonparser.ObjectEach(b, func(key []byte, value []byte, dataType jsonparser.ValueType, offset int) error {
		if string(key) == "timestamp" ||
			string(key) == "utimestamp" ||
			string(key) == "success" ||
			strings.Contains(string(key), "_") { // ignore fields with underscore, like "_locked" and "_debt"
			return nil
		}

		bFreeze, _, _, err := jsonparser.Get(b, string(key)+"_locked")
		if err != nil {
			return fmt.Errorf("failed to get balance freeze for asset [%s], error: %s", string(key), err)
		}

		freeze, err := strconv.ParseFloat(string(bFreeze), 64)
		if err != nil {
			return err
		}

		available, err := strconv.ParseFloat(string(value), 64)
		if err != nil {
			return err
		}

		balances = append(balances, APIBalance{
			Asset:     string(key),
			Available: available,
			Freeze:    freeze,
		})

		return nil
	})
	if err != nil {
		return []APIBalance{}, err
	}

	return balances, nil
}

func (a *API) Balance() ([]APIBalance, error) {
	req, err := a.newHttpRequest(http.MethodPost, apiBalance, nil, private)
	if err != nil {
		return []APIBalance{}, fmt.Errorf("failed to request api [%s], error: %s", apiBalance, err)
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return []APIBalance{}, fmt.Errorf("failed to request api [%s], error: %s", apiBalance, err)
	}

	balances, err := balancesToCommon(httpRes)
	if err != nil {
		return []APIBalance{}, fmt.Errorf("failed to request api [%s], error: %s", apiBalance, err)
	}

	return balances, nil
}

type APIPrice struct {
	Price  float64 `json:"price"`
	Amount float64 `json:"amount"`
}

type APIDepth struct {
	Bids      []APIPrice `json:"bids"`
	Asks      []APIPrice `json:"asks"`
	Timestamp string     `json:"timestamp"`
}

func (a *API) Depth(market string) (APIDepth, error) {
	apiDethParsed := fmt.Sprintf(apiDepth, strings.ToLower(market))
	req, err := a.newHttpRequest(http.MethodGet, apiDethParsed, nil, public)
	if err != nil {
		return APIDepth{}, err
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return APIDepth{}, fmt.Errorf("failed to request api [%s], error: %s", apiDethParsed, err)
	}

	var res APIDepth
	err = json.Unmarshal(httpRes, &res)
	if err != nil {
		return APIDepth{}, fmt.Errorf("failed to request api [%s], error: %s", apiDethParsed, err)
	}

	return res, err
}

type APITrade struct {
	Timestamp string  `json:"timestamp"`
	Market    string  `json:"market"`
	Type      string  `json:"type"`
	Price     float64 `json:"price"`
	Amount    float64 `json:"amount"`
}

const tradeTimeLayout = "2006-01-02 15:04:05"

var tradeTimeLocation = time.FixedZone("GMT-3", -3*60*60)

func (t APITrade) Time() (time.Time, error) {
	return time.ParseInLocation(tradeTimeLayout, t.Timestamp, tradeTimeLocation)
}

func (a *API) Trades(market string) (res []APITrade, err error) {
	apiTradesParsed := fmt.Sprintf(apiTrades, strings.ToLower(market))
	req, err := a.newHttpRequest(http.MethodGet, apiTradesParsed, nil, public)
	if err != nil {
		return res, err
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return res, fmt.Errorf("failed to request api [%s], error: %s", apiTradesParsed, err)
	}

	err = json.Unmarshal(httpRes, &res)
	if err != nil {
		return res, fmt.Errorf("failed to request api [%s], error: %s", apiTradesParsed, err)
	}

	return res, err
}

type APIOpenOrders struct {
	ID         string      `json:"id"`
	Market     string      `json:"market"`
	Type       string      `json:"type"`
	Status     OrderStatus `json:"status"`
	PercentFee string      `json:"percent_fee"` // ASSET FEE
	Canceled   string      `json:"canceled"`    // FALSE = 0 TRUE = 1
	Limited    string      `json:"limited"`     // FALSE = 0 TRUE = 1
	Amount     float64     `json:"amount"`
	Price      float64     `json:"price"`
	ExecAmount float64     `json:"exec_amount"`
	Fee        float64     `json:"fee"`
	Cost       float64     `json:"cost"`
}

// OpenOrders retrieves the open orders for the specified market.
// If no market is provided, returns all open orders.
// The argument format expected is "BTC-BRL"
func (a *API) OpenOrders(market string) ([]APIOpenOrders, error) {
	params := map[string]string{"market": market}
	req, err := a.newHttpRequest(http.MethodPost, apiOpenOrders, params, private)
	if err != nil {
		return []APIOpenOrders{}, fmt.Errorf("failed to request api [%s], error: %s", apiOpenOrders, err)
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return []APIOpenOrders{}, fmt.Errorf("failed to request api [%s], error: %s", apiOpenOrders, err)
	}

	var orders []APIOpenOrders
	if err = json.Unmarshal(httpRes, &orders); err != nil {
		return []APIOpenOrders{}, fmt.Errorf("failed to request api [%s], error: %s", apiOpenOrders, err)
	}

	return orders, nil
}

type APIPlaceOrder struct {
	Success    bool    `json:"success"`
	OrderId    string  `json:"order_id"`
	Type       string  `json:"type"`
	Market     string  `json:"market"`
	Timestamp  string  `json:"timestamp"`
	MessageCod string  `json:"message_cod"`
	Amount     float64 `json:"amount"`
	ExecAmount float64 `json:"exec_amount"`
	Price      float64 `json:"price"`
}

func (a *API) PlaceOrder(side string, params map[string]string) (APIPlaceOrder, error) {
	apiPlaceOrderParsed := fmt.Sprintf(apiPlaceOrder, side)
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrderParsed, params, private)
	if err != nil {
		return APIPlaceOrder{}, fmt.Errorf("failed to request api [%s], error: %s", apiPlaceOrderParsed, err)
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return APIPlaceOrder{}, fmt.Errorf("failed to request api [%s], error: %s", apiPlaceOrderParsed, err)
	}

	var order APIPlaceOrder
	if err = json.Unmarshal(httpRes, &order); err != nil {
		return APIPlaceOrder{}, fmt.Errorf("failed to request api [%s], error: %s", apiPlaceOrderParsed, err)
	}

	return order, nil
}

func (a *API) CancelOrder(orderID string) error {
	params := map[string]string{"order_id": orderID}
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, params, private)
	if err != nil {
		return fmt.Errorf("failed to request api [%s], error: %s", apiCancelOrder, err)
	}

	if _, err = a.makeHttpRequest(req); err != nil {
		return fmt.Errorf("failed to request api [%s], error: %s", apiCancelOrder, err)
	}

	return nil
}

type OrderDetail struct {
	OrderID    string      `json:"id"`
	Market     string      `json:"market"`
	Type       string      `json:"type"`
	Status     OrderStatus `json:"status"`
	Limited    string      `json:"limited"`
	Canceled   string      `json:"canceled"`
	TimeStamp  string      `json:"time_stamp"`
	Concluded  string      `json:"concluded"`
	Amount     float64     `json:"amount"`
	Price      float64     `json:"price"`
	ExecAmount float64     `json:"exec_amount"`
	Cost       float64     `json:"cost"`
	Fee        float64     `json:"fee"`
}
type APIOrderStatus struct {
	Success      bool        `json:"success"`
	OrderDetails OrderDetail `json:"order"`
}

func (a *API) OrderStatus(orderID string) (OrderDetail, error) {
	params := map[string]string{"order_id": orderID}
	req, err := a.newHttpRequest(http.MethodPost, apiOrderStatus, params, private)
	if err != nil {
		return OrderDetail{}, fmt.Errorf("failed to request api [%s], error: %s", apiOrderStatus, err)
	}

	httpRes, err := a.makeHttpRequest(req)
	if err != nil {
		return OrderDetail{}, fmt.Errorf("failed to request api [%s], error: %s", apiOrderStatus, err)
	}

	var order APIOrderStatus
	if err = json.Unmarshal(httpRes, &order); err != nil {
		return OrderDetail{}, fmt.Errorf("failed to request api [%s], error: %s", apiOrderStatus, err)
	}

	return order.OrderDetails, nil
}

// validateHttpResponse tries to read the validation fields from the request,
// some Endpoints may not return the response in properly format then we just
// return the body and let the caller handling the response properly
func validateHttpResponse(httpRes []byte) error {
	// The api is returning an array, so we cannot validate the response
	// in that case we will just send the response to the caller to handle it
	if len(httpRes) > 0 && string(httpRes[0]) == "[" {
		return nil
	}

	var res struct {
		Success    bool   `json:"success"`
		MessageCod string `json:"message_cod"`
	}
	if err := json.Unmarshal(httpRes, &res); err != nil {
		return err
	}

	if !res.Success {
		return fmt.Errorf("msg: %s", res.MessageCod)
	}
	return nil
}

func (a *API) newHttpRequest(method string, endpoint string, params map[string]string, isPrivate bool) (*http.Request, error) {
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	if isPrivate {
		_ = writer.WriteField("auth_token", a.options.ApiSecret+a.options.ApiKey)
		err := writer.Close()
		if err != nil {
			return &http.Request{}, err
		}
	}

	for key, value := range params {
		_ = writer.WriteField(key, value)
		err := writer.Close()
		if err != nil {
			return &http.Request{}, err
		}
	}

	endpoint = path.Join(endpoint)
	u, err := url.Parse(a.baseURL)
	if err != nil {
		return &http.Request{}, err
	}
	urlParsed := u.ResolveReference(&url.URL{Path: endpoint})

	req, err := http.NewRequest(method, urlParsed.String(), payload)
	if err != nil {
		return &http.Request{}, err
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	return req, nil
}

func (a *API) makeHttpRequest(req *http.Request) ([]byte, error) {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return []byte{}, err
	}
	defer res.Body.Close()

	if res.StatusCode < 200 || res.StatusCode > 299 {
		return []byte{}, fmt.Errorf("failed to make http request: %v", res.Status)
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return []byte{}, err
	}

	if err = validateHttpResponse(body); err != nil {
		return []byte{}, err
	}

	return body, nil
}
