package bitypreco

import (
	"log"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api           *API
	options       gateway.Options
	tickCh        chan gateway.Tick
	orderTracking *utils.OrderTracking
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	accGtw := &AccountGateway{
		api:           api,
		options:       options,
		tickCh:        tickCh,
		orderTracking: utils.NewOrderTracking(),
	}
	accGtw.orderTracking.SetUpdateFunc(accGtw.updateOrderStatus)
	return accGtw
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	accountBalance, err := g.api.Balance()
	if err != nil {
		return []gateway.Balance{}, err
	}

	balances := make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Asset),
			Available: balance.Available,
			Total:     balance.Available + balance.Freeze,
		})
	}
	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return orders, nil
}

const notEnoughBalanceErrMatch = "NOT_ENOUGH_USER_BALANCE"
const rateLimitErrMatch = "RATE_LIMIT_EXCEEDED"

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else if order.Side == gateway.Ask {
		side = "sell"
	}

	params := map[string]string{
		"market":  order.Market.Symbol,
		"limited": "true",
		"amount":  utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":   utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	placedOrder, err := g.api.PlaceOrder(side, params)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), notEnoughBalanceErrMatch):
			return "", gateway.InsufficientBalanceErr
		case strings.Contains(err.Error(), rateLimitErrMatch):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	g.orderTracking.TrackAndMonitorOrder(placedOrder.OrderId)

	return placedOrder.OrderId, err
}

const cantCancelFilledOrderErrMatch = "CANT_CANCEL_FILLED_ORDER"

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	// Stop monitoring the order while we cancel it.
	// This is necessary, since the monitoring might be triggered
	// while we wait for the cancel order to be processed.
	g.orderTracking.LockMonitoring(order.ID)

	err := g.api.CancelOrder(order.ID)
	if err != nil {
		switch {
		// Order in final stage, stop tracking
		case strings.Contains(err.Error(), cantCancelFilledOrderErrMatch):
			err = gateway.AlreadyCancelledErr
		// All errors below, are not final stage errors, we should keep tracking
		case strings.Contains(err.Error(), rateLimitErrMatch):
			err = gateway.RateLimitErr
			fallthrough
		default:
			g.orderTracking.UnlockMonitoring(order.ID)
			return err
		}
	}

	g.orderTracking.UntrackOrderWithUpdate(order.ID)
	g.orderTracking.UnlockMonitoring(order.ID)

	return err
}

// updateOrderStatus sends the state of the order to the gateway.Tick channel.
// It retrieves the current order status from the API for the specified order ID.
// If an error occurs during the retrieval, the error is returned.
// Otherwise, it constructs a gateway.OrderUpdateEvent based on the order status
// and sends it through the tickCh channel.
func (g *AccountGateway) updateOrderStatus(orderID string, step utils.OrderTrackingStep) error {
	o, err := g.api.OrderStatus(orderID)
	if err != nil {
		notFoundErr := strings.Contains(err.Error(), "ORDERID_NOT_FOUND")

		if step == utils.OrderTrackingUntrackStep && notFoundErr {
			// If the order is not found, it means it was not filled at all, and it was not saved.
			// In this case, the API will return 404m, and we should not return an error.
			// Consider nothing was filled.
			return nil
		} else {
			if notFoundErr {
				g.orderTracking.UntrackOrder(orderID)
				log.Printf("%s order tracking WARNING - order [%s] not found, stopped tracking", Exchange, orderID)
			}
			return err
		}
	}

	var avg float64
	if o.ExecAmount > 0 {
		avg = o.Cost / o.ExecAmount
	}

	evOrder := gateway.Order{
		ID:               o.OrderID,
		Type:             gateway.LimitOrder,
		State:            mapAPIOrderStateToCommon(o.Status),
		Side:             mapAPIOrderTypeToCommonSide(o.Type),
		Price:            o.Price,
		Amount:           o.Amount,
		FilledAmount:     o.ExecAmount,
		RemainingAmount:  o.Amount - o.ExecAmount,
		FilledMoneyValue: o.Cost,
		Fee:              o.Fee,
		AvgPrice:         avg,
	}

	// If the status of the order is different from "OPEN" or "PARTIALLY" then we can untrack the order.
	if o.Status != OPEN && o.Status != PARTIALLY {
		g.orderTracking.UntrackOrder(orderID)
	}

	g.tickCh <- gateway.TickWithEvents(gateway.NewOrderUpdateEvent(evOrder))

	return nil
}
