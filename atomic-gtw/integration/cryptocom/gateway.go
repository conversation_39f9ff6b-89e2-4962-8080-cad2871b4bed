package cryptocom

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Crypto.com",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseURL(options), options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh, wsAccountURL(g.options))
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(wsFeedURL(g.options), g.options, g.tickCh, g.api)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("failed to subscribe to account updates, err %s", err)
		}
	}

	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol APISymbol) gateway.Market {
	marketType := gateway.SpotMarket
	if symbol.InstType == "PERPETUAL_SWAP" || symbol.InstType == "FUTURE" {
		marketType = gateway.FuturesMarket
	}

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Symbol,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseCurrency),
			Quote: strings.ToUpper(symbol.QuoteCurrency),
		},
		PriceTick:       symbol.PriceTickSize,
		AmountTick:      symbol.QuantityTickSize,
		Closed:          !symbol.Tradable,
		MarketType: marketType,
	}
}

func mapAPIOrderToCommon(o APIOrder) gateway.Order {
	return gateway.Order{
		Market: gateway.Market{
			Exchange: Exchange,
			Symbol:   o.InstrumentName,
		},
		ID:               o.OrderId,
		ClientOrderID:    o.ClientOid,
		Side:             mapAPISideToCommon(o.Side),
		State:            mapAPIOrderStateToCommon(o.Status),
		Amount:           o.Quantity,
		Price:            o.Price,
		AvgPrice:         o.AvgPrice,
		FilledAmount:     o.CumulativeQuantity,
		FilledMoneyValue: o.CumulativeValue,
		PostOnly:         slicesContainsString(o.ExecInst, "POST_ONLY"),
	}
}

func slicesContainsString(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

func mapAPISideToCommon(sd string) gateway.Side {
	if sd == "BUY" {
		return gateway.Bid
	} else if sd == "SELL" {
		return gateway.Ask
	}

	return ""
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "NEW":
		return gateway.OrderOpen
	case "PENDING":
		return gateway.OrderOpen
	case "ACTIVE":
		return gateway.OrderOpen
	case "REJECTED":
		return gateway.OrderCancelled
	case "CANCELED":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "EXPIRED":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}

func apiBaseURL(options gateway.Options) string {
	if options.APIBaseURL != "" {
		return options.APIBaseURL
	}
	if options.Staging {
		return apiBaseSandbox
	} else {
		return apiBaseProd
	}
}

func wsAccountURL(options gateway.Options) string {
	if options.AccountWSBaseURL != "" {
		return options.AccountWSBaseURL
	}

	if options.Staging {
		return "wss://uat-stream.3ona.co/exchange/v1/user"
	}

	return "wss://stream.crypto.com/exchange/v1/user"
}

func wsFeedURL(options gateway.Options) string {
	if options.WSBaseURL != "" {
		return options.WSBaseURL
	}

	if options.Staging {
		return "wss://uat-stream.3ona.co/exchange/v1/market"
	}

	return "wss://stream.crypto.com/exchange/v1/market"
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}

	return depthBook, nil
}

func (gtw *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}
