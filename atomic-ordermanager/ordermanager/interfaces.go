package ordermanager

import (
	"context"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/types"
)

// Re-export types for backward compatibility
type OrderView = types.OrderView
type Fill = types.Fill

type OrderManager interface {
	// Lifecycle Management
	Init(ctx context.Context) error
	ProcessTick(tick gateway.Tick)

	// Imperative Order Commands
	SendOrder(ctx context.Context, params SendOrderParams) (*ManagedOrder, error)
	CancelOrder(ctx context.Context, internalID string) (*ManagedOrder, error)
	CheckOpenOrders(ctx context.Context, market gateway.Market) error

	// State observability
	GetOpenOrders() []*ManagedOrder
	GetPendingOrders() []*ManagedOrder
	GetUnknownOrders() []*ManagedOrder

	// Metrics & Health
	Metrics() Metrics

	// Callbacks
	SetFillCallback(callback func(Fill) error)
}

type AccountGateway interface {
	OpenOrders(gateway.Market) (orders []gateway.Order, err error)
	SendOrder(gateway.Order) (orderID string, err error)
	CancelOrder(gateway.Order) (err error)
}

type Exchange interface {
	ID() string
}
