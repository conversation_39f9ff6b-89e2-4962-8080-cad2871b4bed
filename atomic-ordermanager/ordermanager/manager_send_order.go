package ordermanager

import (
	"context"
	"fmt"
	"sync/atomic"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
	"go.uber.org/zap"
)

const epsilon = 1e-12

type SendOrderParams struct {
	Market   gateway.Market
	Side     gateway.Side
	Type     gateway.OrderType
	Tag      string
	Price    float64
	Amount   float64
	PostOnly bool
}

func (m *manager) SendOrder(ctx context.Context, params SendOrderParams) (*ManagedOrder, error) {
	if !m.init {
		return nil, ErrManagerNotInitialized
	}

	internalID, err := repository.GenerateInternalID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate internal ID: %w", err)
	}

	order := types.NewOrderView(
		params.Market,
		params.Tag,
		params.Side,
		gateway.OrderSent,
		params.Price,
		params.Amount,
		params.PostOnly,
	)

	// Mark order as sent at give time, and set the internal ID.
	order.SetSent()
	order.SetInternalID(internalID)

	gwOrder := gateway.Order{
		Market:   params.Market,
		Type:     params.Type,
		Side:     params.Side,
		Price:    params.Price,
		Amount:   params.Amount,
		PostOnly: params.PostOnly,
	}

	m.logger.Debug("Sending new order",
		zap.String("symbol", order.Symbol),
		zap.String("order_id", order.InternalID),
		zap.Float64("price", order.Price),
		zap.Float64("amount", order.Amount))

	// Now from this point onward, lets stsore this order before sending init
	// If the order submission fails, we still have it in our repository
	managedOrder := NewManagedOrder(order)
	if err := m.storeUpdatedView(managedOrder, order); err != nil {
		managedOrder.Close()
		return nil, fmt.Errorf("failed to create order in repository: %w", err)
	}

	go m.submitOrderAsync(ctx, managedOrder, gwOrder, internalID)

	return managedOrder, nil
}

func (m *manager) submitOrderAsync(ctx context.Context, managedOrder *ManagedOrder, gwOrder gateway.Order, internalID string) {
	defer close(managedOrder.confirmChan)

	select {
	case <-ctx.Done():
		m.handleOrderFailure(managedOrder, gateway.OrderCancelled, fmt.Errorf("context cancelled before submission"), internalID)
		managedOrder.confirmChan <- ctx.Err()
		return
	default:
	}

	orderID, err := m.accGtw.SendOrder(gwOrder)
	if err != nil {
		var newState gateway.OrderState
		if gateway.CheckOrderNotOpenedErr(err) {
			newState = gateway.OrderRejected
		} else {
			newState = gateway.OrderUnknown
		}

		m.handleOrderFailure(managedOrder, newState, err, internalID)
		managedOrder.confirmChan <- err
		return
	}

	m.logger.Debug("Order confirmed by exchange",
		zap.String("internal_id", internalID),
		zap.String("order_id", orderID))

	// Lock order, update it and store it
	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		order.SetConfirmation(orderID)
		order.SetState(gateway.OrderOpen)

		return order, nil
	}); err != nil {
		m.logger.Error("CRITICAL: failed to update confirmed order state",
			zap.String("internal_id", internalID),
			zap.String("order_id", orderID),
			zap.Error(err))
		managedOrder.confirmChan <- err
	}

	// Increment metrics counter atomically
	atomic.AddUint64(&m.ordersSent, 1)

	// Signal successful confirmation
	managedOrder.confirmChan <- nil

	// Check for buffered updates that arrived before confirmation
	if events, ok := m.flushBufferedUpdates(orderID); ok {
		m.logger.Info("Processing buffered events for order",
			zap.Int("count", len(events)),
			zap.String("exchange_order_id", orderID))
		m.processQueuedUpdates(events, managedOrder)
	}
}

// handleOrderFailure updates order state when submission fails
func (m *manager) handleOrderFailure(managedOrder *ManagedOrder, newState gateway.OrderState, originalErr error, internalID string) {
	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		order.SetState(newState)

		if newState == gateway.OrderUnknown {
			order.SetLastErr(originalErr)
		}

		return order, nil
	}); err != nil {
		m.logger.Error("CRITICAL: failed to update failed order state",
			zap.String("internal_id", internalID),
			zap.String("new_state", string(newState)),
			zap.Error(err),
			zap.NamedError("original_err", err))
	}
}
