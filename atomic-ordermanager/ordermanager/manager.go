package ordermanager

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/repository"
	"go.uber.org/zap"
)

type internalOrderID string
type exchangeOrderID string

// manager implements the MarketOrderManager interface.
type manager struct {
	exchange    Exchange
	accGtw      AccountGateway
	repo        repository.Repository
	mu          sync.RWMutex
	logger      *zap.Logger
	init        bool
	exchangeKey string

	// Buffered updates
	bufferedUpdatesMu          sync.Mutex
	bufferedUpdates            map[exchangeOrderID]pendingUpdates
	bufferedUpdatesLastCleanup time.Time

	// Indexed orders
	openOrders     map[internalOrderID]*ManagedOrder
	pendingOrders  map[internalOrderID]*ManagedOrder
	unknownOrders  map[internalOrderID]*ManagedOrder
	allOrders      map[internalOrderID]*ManagedOrder // All orders indexed by internal ID
	exchangeOrders map[exchangeOrderID]internalOrderID

	// Metrics counters (use atomic operations)
	ordersSent      uint64
	ordersCancelled uint64

	// Callbacks
	fillCallback func(Fill) error
}

// New creates a new MarketOrderManager.
func New(
	exchange gateway.Exchange,
	accGtw gateway.AccountGateway,
	logger *zap.Logger,
	repo repository.Repository,
) OrderManager {
	return &manager{
		exchange:        exchange,
		accGtw:          accGtw,
		logger:          logger,
		repo:            repo,
		bufferedUpdates: make(map[exchangeOrderID]pendingUpdates),
		openOrders:      make(map[internalOrderID]*ManagedOrder),
		pendingOrders:   make(map[internalOrderID]*ManagedOrder),
		unknownOrders:   make(map[internalOrderID]*ManagedOrder),
		allOrders:       make(map[internalOrderID]*ManagedOrder),
		exchangeOrders:  make(map[exchangeOrderID]internalOrderID),
	}
}

func (m *manager) Init(ctx context.Context) error {
	startAt := time.Now()

	if err := m.hydrateOrdersByState(gateway.OrderOpen); err != nil {
		return fmt.Errorf("failed to hydrate open orders: %w", err)
	}
	if err := m.hydrateOrdersByState(gateway.OrderPartiallyFilled); err != nil {
		return fmt.Errorf("failed to hydrate open (partially filled) orders: %w", err)
	}
	if err := m.hydrateOrdersByState(gateway.OrderUnknown); err != nil {
		return fmt.Errorf("failed to hydrate open (partially filled) orders: %w", err)
	}
	if err := m.hydrateOrdersByState(gateway.OrderSent); err != nil {
		return fmt.Errorf("failed to hydrate pending orders: %w", err)
	}

	m.logger.Info("Order manager hydration completed", zap.Duration("took", time.Since(startAt)))

	m.exchangeKey = m.exchange.ID()
	m.init = true

	return nil
}

func (m *manager) hydrateOrdersByState(state gateway.OrderState) error {
	orderIDs, err := m.repo.GetOrdersByState(state)
	if err != nil {
		return err
	}

	startAt := time.Now()
	for _, id := range orderIDs {
		orderView, err := m.repo.GetOrder(id)
		if err != nil {
			// Log the error but continue trying to hydrate other orders
			m.logger.Error("failed to get order during hydration",
				zap.String("order_id", id),
				zap.Error(err),
			)
			continue
		}

		if _, err = m.loadManagedOrder(orderView); err != nil {
			return fmt.Errorf("failed to load managed order for ID %s: %w", id, err)
		}
	}

	m.logger.Info("Hydrated orders by state",
		zap.String("state", string(state)),
		zap.Int("count", len(orderIDs)),
		zap.Duration("took", time.Since(startAt)),
	)

	return nil
}

func (m *manager) resolveVersionConflict(currentView, olderView OrderView) OrderView {
	resolved := currentView.MergeWith(olderView)

	m.logger.Warn("Resolved version conflict by merging order views",
		zap.String("internal_id", resolved.InternalID),
		zap.Int("current_version", currentView.Version),
		zap.Int("older_version", olderView.Version),
		zap.String("current_state", string(currentView.State)),
		zap.String("older_state", string(olderView.State)),
		zap.String("resolved_state", string(resolved.State)),
		zap.Float64("current_filled_base", currentView.FilledBase),
		zap.Float64("older_filled_base", olderView.FilledBase),
		zap.Float64("resolved_filled_base", resolved.FilledBase),
	)

	return resolved
}

// Updates a given managed order with a new view immediately. Calls UpdateView to lock the order while executing store/update.
func (m *manager) storeUpdatedView(managedOrder *ManagedOrder, newView OrderView) error {
	return managedOrder.UpdateView(func(currentView OrderView) (OrderView, error) {
		return m.storeOrderFunc(managedOrder, currentView, newView)
	})
}

// Executes a update function while locked order is locked, later, will execute the store/update function.
func (m *manager) updateViewAndStore(managedOrder *ManagedOrder, execFunc UpdateViewFunc) error {
	return managedOrder.UpdateView(func(currentView OrderView) (OrderView, error) {
		newView, err := execFunc(currentView)
		if err != nil {
			return currentView, err
		}

		return m.storeOrderFunc(managedOrder, currentView, newView)
	})
}

func (m *manager) storeOrderFunc(managedOrder *ManagedOrder, currentView, newView OrderView) (OrderView, error) {
	// Check if the view we are trying to store is actually newer than the current one
	if newView.Version < currentView.Version {
		// Resolve the conflict by merging the views
		newView = m.resolveVersionConflict(currentView, newView)
	}

	// Try to update order in storage, if fail, we won't update our internal state
	// and return instead
	if err := m.repo.StoreOrder(newView, currentView.State); err != nil {
		return currentView, fmt.Errorf("failed to update order in repository: %w", err)
	}

	m.mu.Lock()
	m.updateOrderIndex(
		internalOrderID(newView.InternalID),
		exchangeOrderID(newView.ExchangeID),
		newView.State,
		managedOrder,
	)
	m.mu.Unlock()

	return newView, nil
}

func (m *manager) getOrder(internalID string) (*ManagedOrder, bool) {
	m.mu.RLock()
	id := internalOrderID(internalID)
	if order, ok := m.allOrders[id]; ok {
		m.mu.RUnlock()
		return order, true
	}
	m.mu.RUnlock()

	// Fallback to repository lookup (slow)
	orderView, err := m.repo.GetOrder(internalID)
	if err != nil {
		return nil, false
	}

	// Since we fetch this order, we need to load it
	order, err := m.loadManagedOrder(orderView)
	if err != nil {
		return nil, false
	}

	return order, false
}

func (m *manager) getOrderByExchangeID(orderID string) (*ManagedOrder, bool) {
	// Fast lookup in the exchangeOrders map
	m.mu.RLock()
	if internalID, ok := m.exchangeOrders[exchangeOrderID(orderID)]; ok {
		if order, ok := m.allOrders[internalID]; ok {
			m.mu.RUnlock()
			return order, true
		}
	}
	m.mu.RUnlock()

	// Fallback to repository lookup (slow)
	orderView, err := m.repo.GetOrderByOrderID(m.exchangeKey, orderID)
	if err != nil {
		return nil, false
	}

	// Since we fetch this order, we need to load it
	order, err := m.loadManagedOrder(orderView)
	if err != nil {
		return nil, false
	}

	return order, true
}

// Loads a order view from the repository and updates the internal index.
func (m *manager) loadManagedOrder(orderView OrderView) (*ManagedOrder, error) {
	if orderView.InternalID == "" {
		return nil, fmt.Errorf("order view has no internal ID")
	}

	// First check if already loaded
	m.mu.Lock()
	defer m.mu.Unlock()

	if order, ok := m.allOrders[internalOrderID(orderView.InternalID)]; ok {
		return order, nil
	}

	// Since we fetched this order from the repository, lets index it
	order := NewManagedOrder(orderView)
	m.updateOrderIndex(
		internalOrderID(orderView.InternalID),
		exchangeOrderID(orderView.ExchangeID),
		orderView.State,
		order,
	)

	return order, nil
}

func (m *manager) updateOrderIndex(internalID internalOrderID, orderID exchangeOrderID, state gateway.OrderState, order *ManagedOrder) {
	m.allOrders[internalID] = order
	delete(m.pendingOrders, internalID)
	delete(m.openOrders, internalID)
	delete(m.unknownOrders, internalID)

	if state == gateway.OrderSent {
		m.pendingOrders[internalID] = order
	} else if gateway.OrderStateOpen(state) {
		m.openOrders[internalID] = order
	} else if state == gateway.OrderUnknown {
		m.unknownOrders[internalID] = order
	}

	// Reverse lookup for exchange order ID
	// Note that we never clean this up, maybe we should, but it's not a big deal
	// Even with 1M+ entries it won't be much for our modern hardware
	if orderID != "" {
		m.exchangeOrders[exchangeOrderID(orderID)] = internalID
	}
}

func (m *manager) GetOpenOrders() []*ManagedOrder {
	return m.getOrders(m.openOrders)
}

func (m *manager) GetPendingOrders() []*ManagedOrder {
	return m.getOrders(m.pendingOrders)
}

func (m *manager) GetUnknownOrders() []*ManagedOrder {
	return m.getOrders(m.unknownOrders)
}

func (m *manager) getOrders(ordersMap map[internalOrderID]*ManagedOrder) []*ManagedOrder {
	m.mu.RLock()
	defer m.mu.RUnlock()
	orders := make([]*ManagedOrder, 0, len(ordersMap))
	for _, order := range ordersMap {
		orders = append(orders, order)
	}
	return orders
}

type Metrics struct {
	OpenOrdersCount      int    `json:"open_orders_count"`
	PendingOrdersCount   int    `json:"pending_orders_count"`
	OrdersSentTotal      uint64 `json:"orders_sent_total"`
	OrdersCancelledTotal uint64 `json:"orders_cancelled_total"`
	BufferedUpdates      int    `json:"buffered_updates"`
}

func (m *manager) Metrics() Metrics {
	// Acquire read lock only for map lengths
	m.mu.RLock()
	openCount := len(m.openOrders)
	pendingCount := len(m.pendingOrders)
	m.mu.RUnlock()

	// Acquire buffered updates lock separately
	m.bufferedUpdatesMu.Lock()
	bufferedCount := len(m.bufferedUpdates)
	m.bufferedUpdatesMu.Unlock()

	return Metrics{
		OpenOrdersCount:      openCount,
		PendingOrdersCount:   pendingCount,
		OrdersSentTotal:      atomic.LoadUint64(&m.ordersSent),
		OrdersCancelledTotal: atomic.LoadUint64(&m.ordersCancelled),
		BufferedUpdates:      bufferedCount,
	}
}

// SetFillCallback sets a callback function to be called when fills are processed
func (m *manager) SetFillCallback(callback func(Fill) error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.fillCallback = callback
}
