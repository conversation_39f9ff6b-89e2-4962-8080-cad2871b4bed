package ordermanager

import (
	"crypto/sha256"
	"fmt"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/repository"
	"go.uber.org/zap"
)

const (
	maxBufferedUpdatesDuration = 15 * time.Second
)

type pendingUpdates struct {
	time   time.Time
	events []gateway.Event
}

func (m *manager) ProcessTick(tick gateway.Tick) {
	m.processOrQueuedUpdates(tick.EventLog)
}

func (m *manager) processOrQueuedUpdates(events []gateway.Event) {
	if time.Since(m.bufferedUpdatesLastCleanup) > maxBufferedUpdatesDuration {
		m.cleanBufferedUpdates()
		m.bufferedUpdatesLastCleanup = time.Now()
	}

	for _, event := range events {
		switch event.Type {
		case gateway.OrderUpdateEvent:
			orderUpdate := event.Data.(gateway.Order)
			managedOrder, ok := m.getOrderByExchangeID(orderUpdate.ID)
			if ok {
				m.registerOrderUpdate(orderUpdate, managedOrder)
			} else {
				m.bufferEvent(orderUpdate.ID, event)
			}

		case gateway.FillEvent:
			fill := event.Data.(gateway.Fill)
			managedOrder, ok := m.getOrderByExchangeID(fill.OrderID)
			if ok {
				m.registerFill(fill, managedOrder)
			} else {
				m.bufferEvent(fill.OrderID, event)
			}
		}
	}
}

func (m *manager) flushBufferedUpdates(id string) ([]gateway.Event, bool) {
	m.bufferedUpdatesMu.Lock()
	pendingUpdates, ok := m.bufferedUpdates[exchangeOrderID(id)]
	delete(m.bufferedUpdates, exchangeOrderID(id))
	m.bufferedUpdatesMu.Unlock()

	return pendingUpdates.events, ok
}

func (m *manager) processQueuedUpdates(events []gateway.Event, managedOrder *ManagedOrder) {
	for _, event := range events {
		switch event.Type {
		case gateway.OrderUpdateEvent:
			orderUpdate := event.Data.(gateway.Order)
			m.registerOrderUpdate(orderUpdate, managedOrder)

		case gateway.FillEvent:
			fill := event.Data.(gateway.Fill)
			m.registerFill(fill, managedOrder)
		}
	}
}

func (m *manager) bufferEvent(orderID string, event gateway.Event) {
	m.bufferedUpdatesMu.Lock()
	defer m.bufferedUpdatesMu.Unlock()

	pendingUpdate, ok := m.bufferedUpdates[exchangeOrderID(orderID)]
	if !ok {
		pendingUpdate = pendingUpdates{
			time:   time.Now(),
			events: make([]gateway.Event, 0, 1),
		}
	}

	pendingUpdate.events = append(pendingUpdate.events, event)
	m.bufferedUpdates[exchangeOrderID(orderID)] = pendingUpdate
}

func (m *manager) cleanBufferedUpdates() {
	m.bufferedUpdatesMu.Lock()
	defer m.bufferedUpdatesMu.Unlock()

	for orderID, pendingUpdate := range m.bufferedUpdates {
		if time.Since(pendingUpdate.time) > maxBufferedUpdatesDuration {
			delete(m.bufferedUpdates, orderID)
		}
	}
}

// registerOrderUpdate processes an order update and updates the managed order state.
// It handles the logic of updating filled amounts, prices, and states based on the latest order update.
func (m *manager) registerOrderUpdate(orderUpdate gateway.Order, managedOrder *ManagedOrder) {
	logger := loggerManagedOrder(m.logger.With(
		zap.Float64("update_avg_price", orderUpdate.AvgPrice),
		zap.Float64("update_filled_amount", orderUpdate.FilledAmount),
		zap.Float64("update_filled_money_value", orderUpdate.FilledMoneyValue),
		zap.Float64("update_price", orderUpdate.Price),
	), managedOrder)

	// Lock order and update its data
	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		if order.IsTerminated() {
			logger.Warn("order update for terminated order")
			return order, fmt.Errorf("order terminated")
		}

		if orderUpdate.Price != order.Price {
			logger.Warn("order update with different price than current known order state")
		}

		// Check if the order update is more recent than the last known state
		filledBase := orderUpdate.FilledAmount - order.FilledBase
		if filledBase == 0 {
			return order, fmt.Errorf("no change in filled amount")
		} else if filledBase < 0 {
			logger.Warn("order update with lower filled amount than current known order state")
			return order, fmt.Errorf("order update with lower filled amount than current known order state")
		}

		// Not sure we should do this, but lets try it for now. This is risky because this
		// orderUpdate might be stale, it comes from the exchange, and there is a latency
		// between the exchange and our order manager.
		if knownOrderState(orderUpdate.State) && orderUpdate.State != order.State {
			order.SetState(orderUpdate.State)
		}

		// We already know the executed amount, since all integrations are expected to provide a FilledAmount.
		// But we still don't know the executed price, so we need to infer it.
		// Some integrations might also provide the FilledMoneyValue and some a new updated AvgPrice.
		// If only AvgPrice is provided, we need to calculate the executed quote amount.
		// If both are not provided, we default to the order price.
		var currentFilledQuote float64
		if orderUpdate.FilledMoneyValue > 0 {
			currentFilledQuote = orderUpdate.FilledMoneyValue
		} else if orderUpdate.AvgPrice > 0 {
			currentFilledQuote = orderUpdate.AvgPrice * orderUpdate.FilledAmount
		} else {
			orderPrice := max(orderUpdate.Price, order.Price)
			currentFilledQuote = orderPrice * orderUpdate.FilledAmount
		}

		if currentFilledQuote <= 0 {
			logger.Warn("unable to infer current filled quote")
			return order, fmt.Errorf("unable to infer current filled quote, filled money value: %f, avg price: %f, filled amount: %f, order price: %f",
				orderUpdate.FilledMoneyValue, orderUpdate.AvgPrice, orderUpdate.FilledAmount, orderUpdate.Price)
		}

		// Now we can calculate the filled quote and update the managed order state
		// with the new filled amount and filled quote.
		filledQuote := currentFilledQuote - order.FilledQuote
		order.FilledBase = orderUpdate.FilledAmount
		order.FilledQuote = currentFilledQuote
		order.LastFillAt = time.Now()
		if order.FirstFillAt.IsZero() {
			order.FirstFillAt = order.LastFillAt
		}

		// Lets try to calculate the payed fee and update with the current fee.
		filledFee := 0.0
		if orderUpdate.Fee > 0 {
			filledFee = orderUpdate.Fee - order.FilledFee
			order.FilledFee = orderUpdate.Fee
		}
		if orderUpdate.FeeAsset != "" {
			order.FeeAsset = orderUpdate.FeeAsset
		}

		// Generate fill ID and track the fill
		fillPrice := filledQuote / filledBase
		fillID := generateFillID(order.Symbol, order.OrderID, fillPrice, orderUpdate.FilledAmount)
		go m.trackFill(fillID, order, filledBase, fillPrice, filledFee)

		return order, nil
	}); err != nil {
		logger.Warn("failed to update managed order with order update", zap.Error(err))
	}

}

func (m *manager) registerFill(fill gateway.Fill, managedOrder *ManagedOrder) {
	logger := loggerManagedOrder(m.logger.With(
		zap.Float64("fill_amount", fill.Amount),
		zap.Float64("fill_price", fill.Price),
	), managedOrder)

	if fill.Amount == 0 {
		logger.Warn("fill with zero amount")
		return
	}

	if fill.Price <= 0 {
		logger.Warn("invalid fill price")
		return
	}

	// Normalize, this might happen when the exchange reports sells as negative fills.
	if fill.Amount < 0 {
		fill.Amount = -fill.Amount
	}

	// Lock order and update its data
	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		// Check if we already process this fill from the exchange
		existingFill, err := m.repo.CheckFillByExchange(order.ExchangeID, fill.OrderID, fill.ID)
		if err != nil {
			logger.Error("failed to check existing fill, storing it anyways", zap.Error(err))
		} else if existingFill {
			return order, fmt.Errorf("duplicate fill from exchange detected")
		}

		// Check if we are filling more than the order amount
		newFilledBase := order.FilledBase + fill.Amount
		if newFilledBase > order.Amount+epsilon {
			return order, fmt.Errorf("fill exceeds order amount")
		}

		// Update managed order state
		order.FilledBase = newFilledBase
		order.FilledQuote = order.FilledQuote + (fill.Amount * fill.Price)
		order.FilledFee = order.FilledFee + fill.Fee
		order.LastFillAt = time.Now()
		if order.FirstFillAt.IsZero() {
			order.FirstFillAt = order.LastFillAt
		}
		if fill.FeeAsset != "" {
			order.FeeAsset = fill.FeeAsset
		}

		// Check if we can update the order state
		if order.State == gateway.OrderOpen || order.State == gateway.OrderPartiallyFilled {
			if fill.FullyFilled || newFilledBase >= order.Amount-epsilon {
				order.SetState(gateway.OrderFullyFilled)
			} else {
				order.SetState(gateway.OrderPartiallyFilled)
			}
		}

		if fill.ID == "" {
			fill.ID = generateFillID(order.Symbol, order.OrderID, fill.Price, fill.Amount)
			logger.Warn("integration didn't provide fill ID, generating one")
		}

		go m.trackFill(fill.ID, order, fill.Amount, fill.Price, fill.Fee)

		return order, nil
	}); err != nil {
		logger.Warn("failed to update managed order with fill", zap.Error(err))
	}
}

func (m *manager) trackFill(fillID string, order OrderView, amount, price, fee float64) {
	internalOrderID, err := repository.GenerateInternalID()
	if err != nil {
		// TODO: How to recover from this, uuid7 generator should never fail
		// I'm not even sure when it does. In this case, how to we store the fill without a internalID?
		m.logger.Error("failed to generate internal ID for fill",
			zap.String("fill_id", fillID),
			zap.String("order_id", order.InternalID))
		return
	}

	// Create the fill record
	fill := Fill{
		InternalID:      internalOrderID,
		InternalOrderID: order.InternalID,
		TradeID:         fillID,
		OrderID:         order.OrderID,
		ExchangeID:      order.ExchangeID,
		Symbol:          order.Symbol,
		Amount:          amount,
		Price:           price,
		Fee:             fee,
		FeeAsset:        order.FeeAsset,
		Timestamp:       time.Now(),
		Tag:             order.Tag,
		Side:            order.Side,
	}

	// Store the fill
	if err := m.repo.StoreFill(fill); err != nil {
		m.logger.Error("failed to store fill",
			zap.String("fill_id", fillID),
			zap.String("order_id", order.InternalID),
			zap.Error(err))
		return // Don't call callback if storage failed
	}

	// Call fill callback if one is set
	m.mu.RLock()
	callback := m.fillCallback
	m.mu.RUnlock()

	if callback != nil {
		if err := callback(fill); err != nil {
			m.logger.Error("fill callback failed",
				zap.String("fill_id", fillID),
				zap.String("order_id", order.InternalID),
				zap.Error(err))
		}
	}
}

func generateFillID(symbol, orderID string, price, amount float64) string {
	data := fmt.Sprintf("%s:%s:%.8f:%.8f", symbol, orderID, price, amount)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%x", hash[:16]) // Use first 16 bytes as hex string
}

func knownOrderState(st gateway.OrderState) bool {
	if st == gateway.OrderUnknown || st == "" {
		return false
	}
	return true
}
