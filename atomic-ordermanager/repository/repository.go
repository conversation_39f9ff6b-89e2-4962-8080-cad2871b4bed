package repository

import (
	"bytes"
	"encoding/gob"
	"fmt"
	"time"

	"github.com/cockroachdb/pebble"
	"github.com/google/uuid"
	"github.com/herenow/ordermanager/iterator"
	"github.com/herenow/ordermanager/types"
)

// Basics for key composition in PebbleDB
const (
	exchangeFillPrefix = "exchangefill:"
	orderPrefix        = "order:"
	exchangePrefix     = "exchange:"
	statePrefix        = "state:"
	unsyncedPrefix     = "unsynced:"
	fillPrefix         = "fill:"
)

type repository struct {
	db           *pebble.DB
	lastUsedNano uint64
}

func NewRepository(dbPath string) (*repository, error) {
	db, err := pebble.Open(dbPath, &pebble.Options{})
	if err != nil {
		return nil, fmt.Errorf("failed to open pebble db: %w", err)
	}
	return &repository{
		db:           db,
		lastUsedNano: uint64(time.Now().UnixNano()),
	}, nil
}

// encodeData encodes data using the current encoding format (gob)
func (r *repository) encodeData(data interface{}) ([]byte, error) {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	if err := enc.Encode(data); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// decodeData decodes data using the current encoding format (gob)
func (r *repository) decodeData(data []byte, v interface{}) error {
	dec := gob.NewDecoder(bytes.NewReader(data))
	return dec.Decode(v)
}

func (r *repository) Close() error {
	return r.db.Close()
}

// FillIterator returns a fill iterator builder (fluent API)
func (r *repository) FillIterator() iterator.Builder[types.Fill] {
	return iterator.NewRepositoryBuilder(
		func(lastID string, limit int, forward bool) ([]types.Fill, error) {
			return GetItems[types.Fill](r, FillKey, r.endPrefix(FillKey("")), lastID, limit, forward)
		},
		func(fill types.Fill) string { return fill.InternalID },
	)
}

// OrderIterator returns an order iterator builder (fluent API)
func (r *repository) OrderIterator() iterator.Builder[types.OrderView] {
	return iterator.NewRepositoryBuilder(
		func(lastID string, limit int, forward bool) ([]types.OrderView, error) {
			return GetItems[types.OrderView](r, OrderKey, r.endPrefix(OrderKey("")), lastID, limit, forward)
		},
		func(view types.OrderView) string { return view.InternalID },
	)
}

// Generates a uuid V7 that is sortable
func GenerateInternalID() (string, error) {
	id, err := uuid.NewV7()
	if err != nil {
		return "", fmt.Errorf("generate uuid v7: %w", err)
	}
	return id.String(), nil
}

func (r *repository) endPrefix(prefix []byte) []byte {
	end := make([]byte, len(prefix))
	copy(end, prefix)
	for i := len(end) - 1; i >= 0; i-- {
		end[i]++
		if end[i] != 0 {
			return end
		}
	}
	return nil // Overflow
}

func OrderKey(internalID string) []byte {
	return []byte(orderPrefix + internalID)
}

func ExchangeKey(exchange string) []byte {
	return []byte(exchangePrefix + exchange)
}

func ExchangeOrderKey(exchange, orderID string) []byte {
	return append(ExchangeKey(exchange), []byte(":"+orderID)...)
}

func FillKey(internalID string) []byte {
	return []byte(fillPrefix + internalID)
}

func ExchangeFillKey(exchange, orderID, tradeID string) []byte {
	return append(ExchangeKey(exchange), []byte(":"+orderID+":"+tradeID)...)
}

// Composes a key for unsynced orders or fills
func UnsyncedKey(key []byte) []byte {
	return append([]byte(unsyncedPrefix), key...)
}

// Composes a key for entries in a specific state
func StateKey(state string, key []byte) []byte {
	return append([]byte(statePrefix+state+":"), key...)
}
