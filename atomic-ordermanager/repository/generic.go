package repository

import (
	"github.com/cockroachdb/pebble"
)

// Generic iterator methods that eliminate duplication between fills and orders

// getItemsAfter gets items after the given startKey (exclusive, forward iteration)
func getItemsAfter[T any](r *repository, startKey []byte, endKey []byte, limit int) ([]T, error) {
	iter, err := r.db.NewIter(&pebble.IterOptions{
		LowerBound: startKey,
		UpperBound: endKey,
	})
	if err != nil {
		return nil, err
	}
	defer iter.Close()

	var items []T

	// Seek to the start key and skip it if we find an exact match (we want after, not including)
	if len(startKey) > 0 {
		if !iter.SeekGE(startKey) {
			return items, nil
		}
		if iter.Valid() && string(iter.Key()) == string(startKey) {
			iter.Next() // Skip the exact match
		}
	} else {
		// Empty startKey means start from beginning
		iter.First()
	}

	for iter.Valid() && len(items) < limit {
		var item T
		if err := r.decodeData(iter.Value(), &item); err != nil {
			return nil, err
		}
		items = append(items, item)
		iter.Next()
	}

	return items, nil
}

// getItemsBefore gets items before the given endKey (exclusive, reverse iteration)
func getItemsBefore[T any](r *repository, startKey []byte, endKey []byte, limit int) ([]T, error) {
	iter, err := r.db.NewIter(&pebble.IterOptions{
		LowerBound: startKey,
		UpperBound: endKey,
	})
	if err != nil {
		return nil, err
	}
	defer iter.Close()

	var items []T

	// Start from the end and iterate backwards
	iter.Last()
	for iter.Valid() && len(items) < limit {
		var item T
		if err := r.decodeData(iter.Value(), &item); err != nil {
			return nil, err
		}
		items = append(items, item)
		iter.Prev()
	}

	return items, nil
}

// getUnsyncedItems gets unsynced items using generic logic (newest first)
func getUnsyncedItems[T any](r *repository, unsyncedPrefix []byte, getFunc func(string) (T, error), limit int) ([]T, error) {
	iter, err := r.db.NewIter(&pebble.IterOptions{
		LowerBound: unsyncedPrefix,
		UpperBound: r.endPrefix(unsyncedPrefix),
	})
	if err != nil {
		return nil, err
	}
	defer iter.Close()

	var items []T

	// Start from the end and iterate backwards (newest first)
	iter.Last()
	for iter.Valid() && len(items) < limit {
		internalID := string(iter.Key()[len(unsyncedPrefix):])
		item, err := getFunc(internalID)
		if err != nil {
			return items, err
		}
		items = append(items, item)
		iter.Prev()
	}

	return items, nil
}

// getItem gets a single item by ID using generic logic
func getItem[T any](r *repository, key []byte) (item T, err error) {
	data, closer, err := r.db.Get(key)
	if err != nil {
		return item, err
	}
	defer closer.Close()

	if err := r.decodeData(data, &item); err != nil {
		return item, err
	}
	return item, nil
}

// GetItems gets items in the specified direction using generic logic
func GetItems[T any](r *repository, keyFunc func(string) []byte, endPrefix []byte, lastID string, limit int, forward bool) ([]T, error) {
	if lastID == "" {
		if forward {
			// Start from beginning
			return getItemsAfter[T](r, keyFunc(""), endPrefix, limit)
		}
		// Start from end
		return getItemsBefore[T](r, keyFunc(""), endPrefix, limit)
	}
	if forward {
		// Get items after lastID
		return getItemsAfter[T](r, keyFunc(lastID), endPrefix, limit)
	}
	// Get items before lastID
	return getItemsBefore[T](r, keyFunc(""), keyFunc(lastID), limit)
}

// markItemsAsSynced marks items as synced using generic logic
func deleteItemsAsSynced[T any](r *repository, items []T, keyFunc func(string) []byte, getID func(T) string) error {
	batch := r.db.NewBatch()
	defer batch.Close()

	for _, item := range items {
		key := UnsyncedKey(keyFunc(getID(item)))
		if err := batch.Delete(key, nil); err != nil {
			return err
		}
	}

	return batch.Commit(pebble.Sync)
}
