package repository

import (
	"github.com/cockroachdb/pebble"
	"github.com/herenow/ordermanager/types"
)

// Fill operations
func (r *repository) StoreFill(fill Fill) error {
	if fill.InternalID == "" {
		return ErrInternalIDRequired
	} else if fill.ExchangeID == "" {
		return ErrExchangeIDRequired
	} else if fill.OrderID == "" {
		return ErrOrderIDRequired
	} else if fill.TradeID == "" {
		return ErrTradeIDRequired
	}

	batch := r.db.NewBatch()
	defer batch.Close()

	// Store the fill object
	fillKey := FillKey(fill.InternalID)
	data, err := r.encodeData(fill)
	if err != nil {
		return err
	}
	if err := batch.Set(fillKey, data, nil); err != nil {
		return err
	}

	// Index by order ID for quick lookup when checking if fill was already tracked
	exchangeFillKey := ExchangeFillKey(fill.ExchangeID, fill.OrderID, fill.TradeID)
	if err := batch.Set(exchangeFillKey, []byte{}, nil); err != nil {
		return err
	}

	// Add to unsynced fills (all new fills start as unsynced)
	unsyncedFillKey := UnsyncedKey(fillKey)
	if err := batch.Set(unsyncedFillKey, []byte{}, nil); err != nil {
		return err
	}

	return batch.Commit(pebble.Sync)
}

func (r *repository) GetFill(id string) (types.Fill, error) {
	return getItem[types.Fill](r, FillKey(id))
}

func (r *repository) CheckFillByExchange(exchange, orderID, tradeID string) (bool, error) {
	key := ExchangeFillKey(exchange, orderID, tradeID)
	_, closer, err := r.db.Get(key)
	if err != nil {
		if err == pebble.ErrNotFound {
			return false, nil
		} else {
			return false, err
		}
	}
	closer.Close()
	return true, nil
}

func (r *repository) GetUnsyncedFills(limit int) ([]types.Fill, error) {
	return getUnsyncedItems(r, UnsyncedKey(FillKey("")), r.GetFill, limit)
}

func (r *repository) DeleteUnsyncedFills(fills []types.Fill) error {
	return deleteItemsAsSynced(r, fills, FillKey, func(fill types.Fill) string {
		return fill.InternalID
	})
}
