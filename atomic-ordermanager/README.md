# OrderManager: Trading Order Management System

A high-performance order management system for cryptocurrency trading that provides order lifecycle tracking, fill management, and persistence across exchange restarts.

## Overview

OrderManager is a single-market order management system that handles:
- **Order Lifecycle Management**: Track orders from submission through completion
- **Fill Tracking**: Monitor and store all trade executions
- **State Persistence**: Reliable storage using PebbleDB for crash recovery
- **Exchange Integration**: Interface with trading gateways
- **Data Synchronization**: Optional ClickHouse integration for analytics

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Trading Bot    │    │   OrderManager  │    │   Exchange      │
│  (Your Code)    │◄──►│                 │◄──►│   Gateway       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  PebbleDB       │
                       │  Repository     │
                       └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  ClickHouse     │
                       │  (Optional)     │
                       └─────────────────┘
```

## Core Components

### OrderManager
The main interface for order management operations:

```go
type OrderManager interface {
    // Lifecycle
    Init(ctx context.Context) error
    ProcessTick(tick gateway.Tick)

    // Order Operations
    SendOrder(ctx context.Context, params SendOrderParams) (*ManagedOrder, error)
    CancelOrder(ctx context.Context, internalID string) error
    CancelAllOrders(ctx context.Context, filter *OrderFilter) error

    // State Querying
    GetOrderByID(internalID string) (*ManagedOrder, bool)
    GetOpenOrders() []*ManagedOrder
    GetPendingOrders() []*ManagedOrder

    // Metrics
    Metrics() Metrics
}
```

### Repository
Persistent storage layer with PebbleDB backend:

```go
type Repository interface {
    // Order management
    UpdateOrder(order *ManagedOrder, oldState, newState gateway.OrderState) error
    GetOrder(id string) (*ManagedOrder, error)
    GetUnsyncedOrders(limit int) ([]*ManagedOrder, error)

    // Fill management  
    StoreFill(fill *Fill) error
    GetUnsyncedFills(limit int) ([]*Fill, error)

    // Iterator support
    FillIterator() iterator.Builder[*Fill]
    OrderIterator() iterator.Builder[*ManagedOrder]
}
```

### Iterator System
Fluent API for paginated data access:

```go
// Get recent fills with custom batch size
iter := repo.FillIterator().
    Reverse().           // Newest first
    BatchSize(50).       // 50 items per batch
    Build()

for iter.HasNext() {
    fill, ok := iter.Next()
    if !ok {
        break
    }
    // Process fill...
}
```

## Features

### Order Management
- **UUID v7 IDs**: Sortable, time-ordered internal identifiers
- **State Tracking**: Complete order lifecycle from pending to filled/cancelled
- **Exchange ID Mapping**: Bidirectional mapping between internal and exchange order IDs
- **Atomic Updates**: Consistent state updates with proper error handling

### Fill Tracking
- **Automatic Fill Detection**: Process exchange fill events
- **Deduplication**: Prevent duplicate fill recording
- **Relationship Tracking**: Link fills to their originating orders

### Data Persistence
- **PebbleDB Backend**: High-performance embedded key-value store
- **Generic Storage Functions**: Type-safe operations for orders and fills
- **Iterator Support**: Efficient forward/backward pagination
- **Crash Recovery**: Persistent state survives application restarts

### Synchronization
- **Unsynced Tracking**: Track items that need external synchronization
- **ClickHouse Integration**: Optional data warehouse synchronization
- **Batch Processing**: Efficient bulk operations

## Quick Start

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/herenow/atomic-gtw/gateway"
    "github.com/herenow/atomic-gtw/integration/binance"
    "github.com/herenow/ordermanager/ordermanager"
    "github.com/herenow/ordermanager/repository"
    "github.com/herenow/ordermanager/metrics"
)

func main() {
    // Create repository
    repo, err := repository.NewPebbleRepository("./data/orders")
    if err != nil {
        panic(err)
    }
    defer repo.Close()

    // Create order manager
    manager := ordermanager.New(
        binance.Exchange,    // Exchange instance
        gateway,            // Account gateway
        logger,             // Logger instance
        repo,               // Repository
    )

    // Initialize
    ctx := context.Background()
    if err := manager.Init(ctx); err != nil {
        panic(err)
    }

    // Send an order
    order, err := manager.SendOrder(ctx, ordermanager.SendOrderParams{
        Market:   gateway.Market{Symbol: "BTC/USDT"},
        Side:     gateway.SideBuy,
        Type:     gateway.OrderTypeLimit,
        Price:    50000.0,
        Amount:   0.001,
        PostOnly: true,
        Tag:      "quickstart",
    })
    if err != nil {
        panic(err)
    }

    fmt.Printf("Order sent: %s\n", order.View().InternalID)
    
    // Get all open orders
    openOrders := manager.GetOpenOrders()
    fmt.Printf("Open orders: %d\n", len(openOrders))
    
    // Check order status
    for _, openOrder := range openOrders {
        view := openOrder.View()
        fmt.Printf("Order %s: %s, Filled: %.6f/%.6f\n", 
            view.InternalID, view.State, view.FilledBase, view.Amount)
    }
}
```

## Detailed Usage

### OrderManager

The OrderManager is the main interface for managing order lifecycle. It provides thread-safe operations for sending, canceling, and querying orders.

#### Basic Operations

```go
// Create and initialize OrderManager
manager := ordermanager.New(exchange, gateway, logger, repo)
if err := manager.Init(ctx); err != nil {
    panic(err)
}

// Send a limit order
order, err := manager.SendOrder(ctx, ordermanager.SendOrderParams{
    Market:   gateway.Market{Symbol: "BTC/USDT"},
    Side:     gateway.SideBuy,
    Type:     gateway.OrderTypeLimit,
    Price:    45000.0,
    Amount:   0.001,
    PostOnly: true,
    Tag:      "my-strategy",
})

// Cancel an order
cancelledOrder, err := manager.CancelOrder(ctx, order.View().InternalID)

// Check open orders against exchange
err = manager.CheckOpenOrders(ctx, gateway.Market{Symbol: "BTC/USDT"})
```

#### Order State Management

```go
// Get orders by state
openOrders := manager.GetOpenOrders()       // Active orders on exchange
pendingOrders := manager.GetPendingOrders() // Orders being sent
unknownOrders := manager.GetUnknownOrders() // Orders with uncertain state

// Access order details through the View
for _, order := range openOrders {
    view := order.View()
    fmt.Printf("Order %s: %s, Filled: %.6f/%.6f\n", 
        view.InternalID, view.State, view.FilledBase, view.Amount)
    
    // Check order status
    if view.IsFullyFilled() {
        fmt.Printf("Order %s is fully filled\n", view.InternalID)
    } else if view.IsPartiallyFilled() {
        fmt.Printf("Order %s has %.6f remaining\n", view.InternalID, view.RemainingAmount())
    }
}
```

#### Fill Callbacks

```go
// Set up fill callback for real-time fill processing
manager.SetFillCallback(func(fill ordermanager.Fill) error {
    fmt.Printf("Fill: %s, Symbol: %s, Price: %.2f, Amount: %.6f, Fee: %.6f %s\n", 
        fill.InternalID, fill.Symbol, fill.Price, fill.Amount, fill.Fee, fill.FeeAsset)
    return nil
})

// Process ticks to update order states
go func() {
    for tick := range tickChannel {
        manager.ProcessTick(tick)
    }
}()
```

#### Metrics Access

```go
// Get real-time metrics
metrics := manager.Metrics()
fmt.Printf("Open: %d, Pending: %d, Total Sent: %d\n", 
    metrics.OpenOrdersCount, 
    metrics.PendingOrdersCount, 
    metrics.OrdersSentTotal)
```

### Iterator System

The Iterator provides efficient pagination through orders and fills with a fluent API.

#### Basic Iteration

```go
// Iterate through fills (newest first by default)
iter := repo.FillIterator().Build()
defer iter.Close()

for iter.HasNext() {
    fill, ok := iter.Next()
    if !ok {
        break
    }
    fmt.Printf("Fill: %s at %.2f for %s\n", fill.InternalID, fill.Price, fill.Symbol)
}
```

#### Advanced Configuration

```go
// Custom iteration with specific direction and batch size
iter := repo.FillIterator().
    Forward().           // Oldest to newest
    BatchSize(50).       // 50 items per batch
    StartAfter(lastID).  // Start after specific ID
    Build()

// Iterate through orders in reverse (newest first)
orderIter := repo.OrderIterator().
    Reverse().
    BatchSize(100).
    Build()
    
defer orderIter.Close()
for orderIter.HasNext() {
    orderView, ok := orderIter.Next()
    if !ok {
        break
    }
    fmt.Printf("Order: %s, State: %s, Symbol: %s\n", 
        orderView.InternalID, orderView.State, orderView.Symbol)
}
```

#### Iterator Features

- **Fluent API**: Chain methods for readable configuration
- **Bidirectional**: Forward (oldest→newest) or Reverse (newest→oldest)
- **Batch Processing**: Configurable batch sizes for memory efficiency
- **UUID v7 Aware**: Handles time-ordered UUIDs correctly
- **Cursor-based**: Resume iteration from any point using StartAfter()

### Metrics System

The Metrics system provides real-time fill tracking and aggregation across multiple time ranges.

#### Basic Setup

```go
// Create metrics manager
metricsManager := metrics.NewManager(metrics.ManagerConfig{
    Logger: logger,
})

// Set up with repository
if err := metricsManager.Setup(ctx, repo); err != nil {
    panic(err)
}

// Integrate with OrderManager
manager.SetFillCallback(func(fill ordermanager.Fill) error {
    return metricsManager.OnFill(fill)
})
```

#### Custom Time Ranges

```go
// Create custom fill metrics with specific time ranges
fillMetrics := metrics.NewFillMetrics(metrics.FillMetricsConfig{
    TimeRanges: []time.Duration{
        15 * time.Minute,
        1 * time.Hour,
        4 * time.Hour,
        24 * time.Hour,
    },
    HydrateFromDuration: 48 * time.Hour, // Load historical data
    Logger: logger,
})

// Register with registry
registry := metrics.NewRegistry(metrics.DefaultRegistryConfig(logger))
registry.RegisterAdapter("custom_fills", fillMetrics)
```

#### Querying Metrics

```go
// Get metrics for specific market and time range
fillMetrics, err := metricsManager.GetFillMetrics("BTC/USDT", time.Hour)
if err != nil {
    panic(err)
}

fmt.Printf("Market: %s, Last Hour:\n", fillMetrics.Market)
fmt.Printf("  Buy Volume: %.6f, Quote Volume: %.2f, Avg Price: %.2f\n", 
    fillMetrics.Buy.Volume, fillMetrics.Buy.QuoteVolume, fillMetrics.Buy.CalculateAvgPrice())
fmt.Printf("  Sell Volume: %.6f, Quote Volume: %.2f, Avg Price: %.2f\n", 
    fillMetrics.Sell.Volume, fillMetrics.Sell.QuoteVolume, fillMetrics.Sell.CalculateAvgPrice())
fmt.Printf("  Net Volume: %.6f, Net Quote: %.2f, Total Trades: %d\n", 
    fillMetrics.NetVolume(), fillMetrics.NetQuoteVolume(), fillMetrics.TotalCount())
```

#### Comprehensive Integration

```go
// Full integration example
func setupTradingSystem() {
    // 1. Create repository
    repo, _ := repository.NewPebbleRepository("./data")
    defer repo.Close()

    // 2. Create metrics manager
    metricsManager := metrics.NewManager(metrics.ManagerConfig{
        Logger: logger,
    })
    metricsManager.Setup(ctx, repo)

    // 3. Create order manager
    manager := ordermanager.New(exchange, gateway, logger, repo)
    manager.Init(ctx)

    // 4. Connect fill callback
    manager.SetFillCallback(func(fill ordermanager.Fill) error {
        return metricsManager.OnFill(fill)
    })

    // 5. Start processing
    go processMarketData(manager)
    go monitorMetrics(metricsManager)
}

func monitorMetrics(manager *metrics.Manager) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for range ticker.C {
        // Get all metrics
        allMetrics, _ := manager.GetAllMetrics()
        
        for market, timeRanges := range allMetrics {
            for duration, fillMetrics := range timeRanges {
                fmt.Printf("%s (%v): Vol=%.2f, Quote=%.2f, Trades=%d\n", 
                    market, duration, fillMetrics.TotalVolume(), 
                    fillMetrics.TotalQuoteVolume(), fillMetrics.TotalCount())
            }
        }
    }
}
```

## Simulator

The project includes a trading simulator for testing and demonstration:

```bash
cd cmd/simulator
go run . --market "BTC/USDT" --duration 5m
```

Features:
- **Mock Exchange**: Simulated trading environment
- **Real-time UI**: Terminal-based order and fill display
- **ClickHouse Integration**: Optional data warehouse sync
- **Configurable**: Adjustable delays, market behavior

## Project Structure

```
ordermanager/
├── cmd/simulator/          # Trading simulator
├── iterator/              # Fluent pagination API
├── ordermanager/          # Core order management
├── repository/           # PebbleDB persistence layer
├── syncer/              # ClickHouse synchronization
└── README.md
```

## Dependencies

- **PebbleDB**: Embedded key-value store
- **ClickHouse**: Optional analytics database
- **atomic-gtw**: Exchange gateway interface
- **UUID v7**: Time-ordered identifiers

## Configuration

The simulator supports configuration via command-line flags:

```bash
--market string          Trading pair (default "BTC/USDT")
--duration duration      Run duration (default 30m)
--db-path string        Database path (default "./data/ordermanager")
--clickhouse-dsn string ClickHouse connection string
--clean                 Clean database on startup
--log-level string      Logging level (default "info")
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]