package syncer

import (
	"context"
	"fmt"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/repository"
	"go.uber.org/zap"
)

type ClickhouseSyncer struct {
	conn   driver.Conn
	logger *logger.Logger
}

// NewClickhouseSyncer creates a new syncer for ClickHouse.
func NewClickhouseSyncer(conn driver.Conn, logger *logger.Logger) *ClickhouseSyncer {
	return &ClickhouseSyncer{
		conn:   conn,
		logger: logger,
	}
}

// Flush syncs unsynced orders and fills to ClickHouse
func (s *ClickhouseSyncer) Flush(ctx context.Context, repo repository.Repository) error {
	s.logger.Info("Starting ClickHouse sync")

	// Sync orders
	if err := s.syncOrders(ctx, repo); err != nil {
		s.logger.Error("Failed to sync orders", zap.Error(err))
		return fmt.Errorf("failed to sync orders: %w", err)
	}

	// Sync fills
	if err := s.syncFills(ctx, repo); err != nil {
		s.logger.Error("Failed to sync fills", zap.Error(err))
		return fmt.Errorf("failed to sync fills: %w", err)
	}

	s.logger.Info("ClickHouse sync completed successfully")
	return nil
}

func (s *ClickhouseSyncer) syncOrders(ctx context.Context, repo repository.Repository) error {
	// Get unsynced orders from the repository
	orders, err := repo.GetUnsyncedOrders(1000)
	if err != nil {
		return fmt.Errorf("failed to get unsynced orders: %w", err)
	}

	if len(orders) == 0 {
		s.logger.Debug("No unsynced orders to sync")
		return nil
	}

	s.logger.Info("Syncing orders to ClickHouse", zap.Int("count", len(orders)))

	// Prepare batch insert statement
	batch, err := s.conn.PrepareBatch(ctx, `
		INSERT INTO orders (
			internal_id, exchange_id, order_id, client_order_id, symbol, 
			state, previous_states, side, price, amount, fee_asset, filled_base, filled_quote, filled_fee,
			first_fill_at, last_fill_at, tag, sent_at, confirmed_at, canceled_at, canceling,
			cancel_requested_at, cancel_deadline, soft_match, last_update_at, version,
			presumed_canceled_count, unexpectedly_open_count
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to prepare batch: %w", err)
	}
	defer batch.Abort()

	// Add rows to batch
	for _, order := range orders {
		previousStates := make([]string, len(order.PreviousStates))
		for i, state := range order.PreviousStates {
			previousStates[i] = string(state)
		}
		err := batch.Append(
			order.InternalID, order.ExchangeID, order.OrderID, order.ClientOrderID,
			order.Symbol, string(order.State), previousStates, string(order.Side), order.Price, order.Amount,
			order.FeeAsset, order.FilledBase, order.FilledQuote, order.FilledFee,
			order.FirstFillAt, order.LastFillAt, order.Tag, order.SentAt, order.ConfirmedAt, order.CanceledAt, order.Canceling,
			order.CancelRequestedAt, order.CancelDeadline, order.SoftMatch, order.LastUpdateAt, order.Version,
			order.PresumedCanceledCount, order.UnexpectedlyOpenCount,
		)
		if err != nil {
			return fmt.Errorf("failed to append order to batch: %w", err)
		}
	}

	// Execute batch insert
	if err := batch.Send(); err != nil {
		return fmt.Errorf("failed to send batch: %w", err)
	}

	// Mark orders as synced
	if err := repo.DeleteUnsyncedOrders(orders); err != nil {
		return fmt.Errorf("failed to mark orders as synced: %w", err)
	}

	return nil
}

func (s *ClickhouseSyncer) syncFills(ctx context.Context, repo repository.Repository) error {
	// Get unsynced fills from the repository
	fills, err := repo.GetUnsyncedFills(1000)
	if err != nil {
		return fmt.Errorf("failed to get unsynced fills: %w", err)
	}

	if len(fills) == 0 {
		s.logger.Debug("No unsynced fills to sync")
		return nil
	}

	s.logger.Info("Syncing fills to ClickHouse", zap.Int("count", len(fills)))

	// Prepare batch insert statement
	batch, err := s.conn.PrepareBatch(ctx, `
		INSERT INTO fills (
			internal_id, internal_order_id, trade_id, order_id, exchange_id, symbol, side, amount, price, fee, fee_asset, ts, tag
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to prepare batch: %w", err)
	}
	defer batch.Abort()

	// Add rows to batch
	for _, fill := range fills {
		err := batch.Append(
			fill.InternalID, fill.InternalOrderID, fill.TradeID, fill.OrderID, fill.ExchangeID, fill.Symbol, fill.Side, fill.Amount,
			fill.Price, fill.Fee, fill.FeeAsset, fill.Timestamp, fill.Tag,
		)
		if err != nil {
			return fmt.Errorf("failed to append fill to batch: %w", err)
		}
	}

	// Execute batch insert
	if err := batch.Send(); err != nil {
		return fmt.Errorf("failed to send batch: %w", err)
	}

	// Mark fills as synced
	if err := repo.DeleteUnsyncedFills(fills); err != nil {
		return fmt.Errorf("failed to mark fills as synced: %w", err)
	}

	return nil
}

// CreateOrdersTableSchema returns the ClickHouse DDL for creating the orders table
func CreateOrdersTableSchema() string {
	return `
CREATE TABLE IF NOT EXISTS orders (
	internal_id String,
	exchange_id LowCardinality(String),
	order_id String,
	client_order_id String,
	symbol LowCardinality(String),
	state LowCardinality(String),
	previous_states Array(LowCardinality(String)),
	side LowCardinality(String),
	price Float64,
	amount Float64,
	fee_asset LowCardinality(String),
	filled_base Float64,
	filled_quote Float64,
	filled_fee Float64,
	first_fill_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	last_fill_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	tag LowCardinality(String),
	sent_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	confirmed_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	canceled_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	canceling Bool,
	cancel_requested_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	cancel_deadline DateTime64(6) CODEC(Delta, ZSTD(1)),
	soft_match Bool,
	last_update_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	version Int32,
	presumed_canceled_count Int32,
	unexpectedly_open_count Int32
) ENGINE = ReplacingMergeTree(last_update_at)
PRIMARY KEY (exchange_id, symbol, internal_id)
ORDER BY (exchange_id, symbol, internal_id)
PARTITION BY toYYYYMM(sent_at)`
}

// CreateFillsTableSchema returns the ClickHouse DDL for creating the fills table
func CreateFillsTableSchema() string {
	return `
CREATE TABLE IF NOT EXISTS fills (
	internal_id String,
	internal_order_id String,
	trade_id String,
	order_id String,
	exchange_id LowCardinality(String),
	symbol LowCardinality(String),
	side LowCardinality(String),
	amount Float64,
	price Float64,
	fee Float64,
	fee_asset LowCardinality(String),
	ts DateTime64(6) CODEC(Delta, ZSTD(1)),
	tag LowCardinality(String)
) ENGINE = MergeTree()
PRIMARY KEY (exchange_id, symbol, internal_id)
ORDER BY (exchange_id, symbol, internal_id)
PARTITION BY toYYYYMM(ts)`
}
