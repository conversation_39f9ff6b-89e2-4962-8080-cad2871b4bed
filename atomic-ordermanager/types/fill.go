package types

import (
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type Fill struct {
	InternalID      string       `json:"internal_id"`
	TradeID         string       `json:"trade_id"` // Trade in on the exchange
	OrderID         string       `json:"order_id"` // Order ID on the exchange
	InternalOrderID string       `json:"internal_order_id"`
	ExchangeID      string       `json:"exchange_id"`
	Symbol          string       `json:"symbol"`
	Side            gateway.Side `json:"side"`
	Amount          float64      `json:"amount"`
	Price           float64      `json:"price"`
	Fee             float64      `json:"fee"`
	FeeAsset        string       `json:"fee_asset"`
	Timestamp       time.Time    `json:"timestamp"`
	Tag             string       `json:"tag"`
}
