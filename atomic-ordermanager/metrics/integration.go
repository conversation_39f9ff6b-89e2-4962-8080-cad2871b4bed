package metrics

import (
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/logger"
	ordermanager "github.com/herenow/ordermanager/ordermanager"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
)

// FillAwareOrderManager is an interface that can be implemented to provide fill callbacks
type FillAwareOrderManager interface {
	// OnFillProcessed is called when a fill is processed
	OnFillProcessed(fill types.Fill) error
}

// CreateOrderManagerWithMetrics creates an OrderManager with metrics integration from scratch
func CreateOrderManagerWithMetrics(
	exchange gateway.Exchange,
	accGtw gateway.AccountGateway,
	orderManagerLogger *logger.Logger,
	repo repository.Repository,
	metricsManager *Manager,
) ordermanager.OrderManager {

	// Create OrderManager first
	om := ordermanager.New(exchange, accGtw, orderManagerLogger, repo)

	// Create callback function
	callback := func(fill types.Fill) error {
		return metricsManager.OnFill(fill)
	}

	// Set the fill callback
	om.SetFillCallback(callback)

	return om
}

// Alternative integration approach using callbacks
func IntegrateWithCallbacks(
	exchange gateway.Exchange,
	accGtw gateway.AccountGateway,
	orderManagerLogger *logger.Logger,
	repo repository.Repository,
	metricsManager *Manager,
) ordermanager.OrderManager {

	return CreateOrderManagerWithMetrics(exchange, accGtw, orderManagerLogger, repo, metricsManager)
}
