package mocks

import (
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

const (
	OrderPending    gateway.OrderState = "pending"
	OrderCancelling gateway.OrderState = "cancelling"
)

// MockGateway is a mock implementation of the AccountGateway interface.
type MockGateway struct {
	mu           sync.Mutex
	orders       map[string]gateway.Order
	delay        time.Duration
	fillCallback func(gateway.Fill) // Callback to send fills to order manager
}

// NewMockGateway creates a new instance of MockGateway.
func NewMockGateway(delay time.Duration) *MockGateway {
	return &MockGateway{
		orders: make(map[string]gateway.Order),
		delay:  delay,
	}
}

// SetFillCallback sets the callback function to send fills to the order manager
func (g *MockGateway) SetFillCallback(callback func(gateway.Fill)) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.fillCallback = callback
}

// OpenOrders returns the list of open orders.
func (g *MockGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	var openOrders []gateway.Order
	for _, order := range g.orders {
		if order.State == gateway.OrderOpen {
			openOrders = append(openOrders, order)
		}
	}
	return openOrders, nil
}

// SendOrder simulates sending an order to the exchange.
func (g *MockGateway) SendOrder(order gateway.Order) (string, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	if rand.Float32() < 0.01 { // 1% chance of failure
		return "", errors.New("failed to send order")
	}

	orderID := fmt.Sprintf("%d", time.Now().UnixNano())
	order.ID = orderID
	order.State = OrderPending // Set to pending initially
	order.FilledAmount = 0     // Initialize filled amount
	g.orders[orderID] = order

	time.Sleep(g.delay)

	if o, ok := g.orders[orderID]; ok && o.State == OrderPending {
		o.State = gateway.OrderOpen // Transition to open after delay
		g.orders[orderID] = o
	}

	return orderID, nil
}

// CancelOrder simulates canceling an order on the exchange.
func (g *MockGateway) CancelOrder(order gateway.Order) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if rand.Float32() < 0.01 { // 1% chance of failure
		return errors.New("failed to cancel order")
	}

	o, ok := g.orders[order.ID]
	if ok && gateway.OrderStateOpen(order.State) {
		o.State = OrderCancelling // Set to cancelling initially
		g.orders[order.ID] = o

		// Simulate asynchronous cancellation processing
		time.Sleep(g.delay)

		if o, ok := g.orders[order.ID]; ok && o.State == OrderCancelling {
			o.State = gateway.OrderCancelled // Transition to cancelled after delay
			g.orders[order.ID] = o
		}
	} else if !ok {
		return fmt.Errorf("order %s not found", order.ID)
	} else {
		return fmt.Errorf("order %s not in cancellable state %s", order.ID, order.State)
	}

	return nil
}

func (g *MockGateway) Balances() ([]gateway.Balance, error) {
	return []gateway.Balance{},
		nil
}

func (g *MockGateway) ReplaceOrder(order gateway.Order) (gateway.Order, error) {
	return gateway.Order{}, errors.New("not implemented")
}

func (g *MockGateway) Positions() ([]gateway.Position, error) {
	return []gateway.Position{},
		nil
}

// GetRegisteredOrders returns all orders that are currently registered (open or pending)
func (g *MockGateway) GetRegisteredOrders() []gateway.Order {
	g.mu.Lock()
	defer g.mu.Unlock()

	var registeredOrders []gateway.Order
	for _, order := range g.orders {
		if order.State == gateway.OrderOpen || order.State == OrderPending {
			registeredOrders = append(registeredOrders, order)
		}
	}
	return registeredOrders
}

// GenerateFillsForRegisteredOrders generates fills for registered orders based on probability
func (g *MockGateway) GenerateFillsForRegisteredOrders(fillProbability float64) {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.fillCallback == nil {
		return
	}

	for _, order := range g.orders {
		// Only generate fills for open orders
		if order.State != gateway.OrderOpen {
			continue
		}

		// Apply probability check
		if rand.Float64() > fillProbability {
			continue
		}

		// Calculate remaining fillable amount
		remainingAmount := order.Amount - order.FilledAmount
		if remainingAmount <= 0 {
			continue // Order is already fully filled
		}

		// Generate a partial fill (10% to 50% of remaining)
		fillPercentage := 0.1 + rand.Float64()*0.4
		fillAmount := remainingAmount * fillPercentage

		// Ensure we don't exceed remaining amount
		if fillAmount > remainingAmount {
			fillAmount = remainingAmount
		}

		// Create fill
		fill := gateway.Fill{
			OrderID:   order.ID,
			Price:     order.Price + (rand.Float64()-0.5)*0.01, // Small price variation
			Amount:    fillAmount,
			Side:      order.Side,
			Timestamp: time.Now(),
			ID:        fmt.Sprintf("trade_%d", time.Now().UnixNano()),
		}

		// Update order's filled amount
		updatedOrder := order
		updatedOrder.FilledAmount += fillAmount
		g.orders[order.ID] = updatedOrder

		// Send fill via callback
		g.fillCallback(fill)
	}
}
