package main

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/cmd/simulator/mocks"
	"github.com/herenow/ordermanager/ordermanager"
	"github.com/herenow/ordermanager/types"
	"go.uber.org/zap"
)

const (
	sendOrderInterval   = 1000 * time.Millisecond // Interval for sending new orders
	maxOrdersPerSide    = 100                     // Maximum orders per side (bid/ask)
	maxSubmittingOrders = 10                      // Maximum concurrent order submissions
	maxCancellingOrders = 20                      // Maximum concurrent order cancellations
)

// simulator encapsulates the logic for running the market order manager simulation.
type simulator struct {
	mom               ordermanager.OrderManager
	market            gateway.Market
	mockGateway       *mocks.MockGateway
	mainCtx           context.Context
	mainCancel        context.CancelFunc
	wg                sync.WaitGroup
	logger            *logger.Logger
	submissionSlots   chan struct{} // Semaphore for limiting concurrent order submissions
	submittingOrders  sync.Map      // Track currently submitting orders
	cancellationSlots chan struct{} // Semaphore for limiting concurrent order cancellations
	cancellingOrders  sync.Map      // Track currently cancelling orders
	fillRate          time.Duration // Rate at which fills are generated
	fillProbability   float64       // Probability of fills being generated
}

// newSimulator creates a new simulator instance.
func newSimulator(ctx context.Context, mom ordermanager.OrderManager, market gateway.Market, mockGateway *mocks.MockGateway, logger *logger.Logger, fillRate time.Duration, fillProbability float64) *simulator {
	mainCtx, mainCancel := context.WithCancel(ctx)

	// Initialize semaphore for limiting concurrent submissions
	submissionSlots := make(chan struct{}, maxSubmittingOrders)
	for i := 0; i < maxSubmittingOrders; i++ {
		submissionSlots <- struct{}{} // Fill all slots initially
	}

	// Initialize semaphore for limiting concurrent cancellations
	cancellationSlots := make(chan struct{}, maxCancellingOrders)
	for i := 0; i < maxCancellingOrders; i++ {
		cancellationSlots <- struct{}{} // Fill all slots initially
	}

	s := &simulator{
		mom:               mom,
		market:            market,
		mockGateway:       mockGateway,
		mainCtx:           mainCtx,
		mainCancel:        mainCancel,
		logger:            logger,
		submissionSlots:   submissionSlots,
		cancellationSlots: cancellationSlots,
		fillRate:          fillRate,
		fillProbability:   fillProbability,
	}
	
	// Set up fill callback for mock gateway
	mockGateway.SetFillCallback(s.processFill)
	
	return s
}

// start begins the simulation, running the main loop and background tasks.
func (s *simulator) start() {
	s.wg.Add(3) // Add tasks for send, generate fills, and check open orders loops
	go s.sendOrdersLoop()
	go s.generateFillsLoop()
	go s.checkOpenOrdersLoop()
}

// stop gracefully shuts down the simulator.
func (s *simulator) stop() {
	s.mainCancel()
	s.wg.Wait() // Wait for all loops to finish
}

// sendOrdersLoop periodically sends new orders to the market.
func (s *simulator) sendOrdersLoop() {
	defer s.wg.Done()
	s.logger.Info("Starting sendOrdersLoop")
	ticker := time.NewTicker(sendOrderInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.mainCtx.Done():
			s.logger.Info("sendOrdersLoop shutting down")
			return
		case <-ticker.C:
			openOrders := s.mom.GetOpenOrders()
			totalOpenOrders := len(openOrders)
			maxTotalOrders := maxOrdersPerSide * 2
			s.logger.Info("Order check", zap.Int("total_open", totalOpenOrders), zap.Int("limit", maxTotalOrders))

			// If we're at max capacity, cancel 25% of orders randomly
			if totalOpenOrders >= maxTotalOrders {
				s.logger.Info("At max capacity, cancelling 25% of orders randomly",
					zap.Int("total_open", totalOpenOrders))
				s.cancelRandomOrders(openOrders, 0.25)
				continue
			}

			// Calculate how many orders we can create this tick
			ordersToCreate := maxTotalOrders - totalOpenOrders
			if ordersToCreate > maxSubmittingOrders {
				// Don't overwhelm the submission slots
				ordersToCreate = maxSubmittingOrders
			}

			s.logger.Info("Creating multiple orders this tick",
				zap.Int("orders_to_create", ordersToCreate),
				zap.Int("current_open", totalOpenOrders))

			// Create multiple orders up to our limit
			for i := 0; i < ordersToCreate; i++ {
				// Try to acquire a submission slot (non-blocking)
				select {
				case <-s.submissionSlots:
					// Randomly choose side
					side := gateway.Bid
					if rand.Float64() < 0.5 {
						side = gateway.Ask
					}

					params := ordermanager.SendOrderParams{
						Market: s.market,
						Tag:    fmt.Sprintf("mkt-maker-%s-%d", side, rand.Intn(10000)),
						Price:  68500.0 + (rand.Float64() * 100) - 50,
						Amount: 0.01 + rand.Float64()*0.09,
						Side:   side,
					}

					// Launch async order submission
					go s.submitOrderAsync(params)

				default:
					// No submission slots available, break out of the loop
					s.logger.Debug("No more submission slots available, stopping order creation",
						zap.Int("created_so_far", i),
						zap.Int("max_submitting", maxSubmittingOrders))
					break
				}
			}
		}
	}
}

// generateFillsLoop periodically generates fills for registered orders via the mock gateway.
func (s *simulator) generateFillsLoop() {
	defer s.wg.Done()
	s.logger.Info("Starting generateFillsLoop", zap.Duration("fill_rate", s.fillRate), zap.Float64("fill_probability", s.fillProbability))
	ticker := time.NewTicker(s.fillRate)
	defer ticker.Stop()

	for {
		select {
		case <-s.mainCtx.Done():
			s.logger.Info("generateFillsLoop shutting down")
			return
		case <-ticker.C:
			// Let the mock gateway generate fills for its registered orders
			s.mockGateway.GenerateFillsForRegisteredOrders(s.fillProbability)
		}
	}
}

// processFill handles fills generated by the mock gateway
func (s *simulator) processFill(fill gateway.Fill) {
	// Create a Tick with the FillEvent
	tick := gateway.Tick{
		EventLog: []gateway.Event{
			{
				Type: gateway.FillEvent,
				Data: fill,
			},
		},
	}
	
	s.logger.Debug("Processing fill", 
		zap.String("order_id", fill.OrderID),
		zap.Float64("amount", fill.Amount),
		zap.Float64("price", fill.Price),
		zap.String("side", string(fill.Side)))
	
	s.mom.ProcessTick(tick)
}

// checkOpenOrdersLoop periodically checks open orders on the exchange.
func (s *simulator) checkOpenOrdersLoop() {
	defer s.wg.Done()
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.mainCtx.Done():
			return
		case <-ticker.C:
			s.logger.Info("Checking open orders on the exchange")
			if err := s.mom.CheckOpenOrders(s.mainCtx, s.market); err != nil {
				s.logger.Error("Failed to check open orders",
					zap.String("symbol", s.market.Symbol),
					zap.Error(err))
			}
		}
	}
}

// submitOrderAsync handles the full async order submission flow with slot management
func (s *simulator) submitOrderAsync(params ordermanager.SendOrderParams) {
	defer func() {
		// Always return the submission slot when done
		s.submissionSlots <- struct{}{}
		s.logger.Info("Returned submission slot")
	}()

	s.logger.Info("Starting order submission", zap.String("side", string(params.Side)))
	// Create order immediately
	order, err := s.mom.SendOrder(s.mainCtx, params)
	s.logger.Info("SendOrder returned", zap.String("side", string(params.Side)), zap.Error(err))
	if err != nil {
		s.logger.Warn("Failed to create order", zap.Error(err))
		return
	}

	view := order.View()
	s.logger.Info("Order created, awaiting exchange confirmation",
		zap.String("order_id", view.InternalID),
		zap.String("side", string(view.Side)),
		zap.Float64("price", view.Price),
		zap.Float64("amount", view.Amount))

	// Track that we're submitting this order
	s.submittingOrders.Store(view.InternalID, true)
	defer s.submittingOrders.Delete(view.InternalID)

	// Wait for confirmation with timeout
	confirmationTimeout := time.NewTimer(5 * time.Second)
	defer confirmationTimeout.Stop()

	select {
	case err := <-order.Confirmed():
		if err != nil {
			s.logger.Warn("Order confirmation failed",
				zap.String("order_id", view.InternalID),
				zap.String("side", string(view.Side)),
				zap.Error(err))
		} else {
			updatedView := order.View() // Get updated view after confirmation
			s.logger.Info("Order confirmed by exchange",
				zap.String("order_id", updatedView.InternalID),
				zap.String("exchange_order_id", updatedView.OrderID),
				zap.String("side", string(updatedView.Side)),
				zap.Float64("price", updatedView.Price),
				zap.Float64("amount", updatedView.Amount))
		}
	case <-confirmationTimeout.C:
		s.logger.Warn("Order confirmation timed out",
			zap.String("order_id", view.InternalID),
			zap.String("side", string(view.Side)))
	case <-s.mainCtx.Done():
		s.logger.Debug("Order confirmation cancelled due to shutdown",
			zap.String("order_id", view.InternalID))
	}
}

// cancelOrderAsync handles the full async order cancellation flow with slot management
func (s *simulator) cancelOrderAsync(order *ordermanager.ManagedOrder) {
	// Try to acquire a cancellation slot (non-blocking)
	select {
	case <-s.cancellationSlots:
		// Slot acquired, proceed with cancellation directly (no additional goroutine)
		s.processCancellationAsync(order)
	default:
		// No cancellation slots available, log and skip
		s.logger.Debug("No cancellation slots available, skipping order cancellation",
			zap.String("order_id", order.View().InternalID),
			zap.Int("max_cancelling", maxCancellingOrders))
	}
}

// processCancellationAsync handles the actual cancellation process
func (s *simulator) processCancellationAsync(order *ordermanager.ManagedOrder) {
	defer func() {
		// Always return the cancellation slot when done
		s.cancellationSlots <- struct{}{}
	}()

	// Initiate cancellation
	orderView := order.View()
	cancelledOrder, err := s.mom.CancelOrder(s.mainCtx, orderView.InternalID)
	if err != nil {
		s.logger.Warn("Failed to initiate order cancellation",
			zap.String("order_id", orderView.InternalID),
			zap.Error(err))
		return
	}

	cancelledView := cancelledOrder.View()
	s.logger.Info("Order cancellation initiated, awaiting confirmation",
		zap.String("order_id", cancelledView.InternalID),
		zap.String("exchange_id", cancelledView.OrderID))

	// Track that we're cancelling this order
	s.cancellingOrders.Store(cancelledView.InternalID, true)
	defer s.cancellingOrders.Delete(cancelledView.InternalID)

	// Wait for cancellation confirmation with timeout
	cancellationTimeout := time.NewTimer(5 * time.Second)
	defer cancellationTimeout.Stop()

	select {
	case err := <-cancelledOrder.Cancelled():
		if err != nil {
			s.logger.Warn("Order cancellation failed",
				zap.String("order_id", cancelledView.InternalID),
				zap.String("exchange_id", cancelledView.OrderID),
				zap.Error(err))
		} else {
			s.logger.Info("Order successfully cancelled",
				zap.String("order_id", cancelledView.InternalID),
				zap.String("exchange_id", cancelledView.OrderID))
		}
	case <-cancellationTimeout.C:
		s.logger.Warn("Order cancellation timed out",
			zap.String("order_id", cancelledView.InternalID),
			zap.String("exchange_id", cancelledView.OrderID))
	case <-s.mainCtx.Done():
		s.logger.Debug("Order cancellation cancelled due to shutdown",
			zap.String("order_id", cancelledView.InternalID))
	}
}

// getSubmittingOrdersCount returns the number of currently submitting orders
func (s *simulator) getSubmittingOrdersCount() int {
	count := 0
	s.submittingOrders.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// cancelAndCreateOrder cancels an order and waits for confirmation before creating a new one
// This function is now deprecated in favor of the new random cancellation strategy
func (s *simulator) cancelAndCreateOrder(orderToCancel *ordermanager.ManagedOrder, newOrderSide gateway.Side) {
	// Try to acquire both slots upfront with proper cleanup
	var hasCancellationSlot, hasSubmissionSlot bool

	// Try to get cancellation slot first
	select {
	case <-s.cancellationSlots:
		hasCancellationSlot = true
	default:
		s.logger.Debug("No cancellation slots available for limit enforcement",
			zap.String("order_to_cancel", orderToCancel.View().InternalID),
			zap.String("new_side", string(newOrderSide)))
		return
	}

	// Try to get submission slot
	select {
	case <-s.submissionSlots:
		hasSubmissionSlot = true
	default:
		// Return cancellation slot and abort
		if hasCancellationSlot {
			s.cancellationSlots <- struct{}{}
		}
		s.logger.Debug("No submission slots available for limit enforcement",
			zap.String("order_to_cancel", orderToCancel.View().InternalID),
			zap.String("new_side", string(newOrderSide)))
		return
	}

	// Ensure slots are returned no matter what
	defer func() {
		if hasCancellationSlot {
			s.cancellationSlots <- struct{}{}
		}
		if hasSubmissionSlot {
			s.submissionSlots <- struct{}{}
		}
	}()

	orderToCancelView := orderToCancel.View()
	s.logger.Info("Enforcing order limit: cancel then create",
		zap.String("cancelling_order", orderToCancelView.InternalID),
		zap.String("new_side", string(newOrderSide)))

	// Step 1: Cancel the old order and wait for confirmation
	cancelledOrder, err := s.mom.CancelOrder(s.mainCtx, orderToCancelView.InternalID)
	if err != nil {
		s.logger.Warn("Failed to initiate cancellation for limit enforcement",
			zap.String("order_id", orderToCancelView.InternalID),
			zap.Error(err))
		return
	}

	// Wait for cancellation to complete with timeout
	cancelTimeout := time.NewTimer(5 * time.Second)
	defer cancelTimeout.Stop()

	var cancelledOrderView types.OrderView
	select {
	case err := <-cancelledOrder.Cancelled():
		if err != nil {
			cancelledOrderView = cancelledOrder.View()
			s.logger.Warn("Order cancellation failed during limit enforcement",
				zap.String("order_id", cancelledOrderView.InternalID),
				zap.Error(err))
			return
		}
		cancelledOrderView = cancelledOrder.View()
		s.logger.Info("Order cancelled successfully, now creating replacement",
			zap.String("cancelled_order", cancelledOrderView.InternalID),
			zap.String("new_side", string(newOrderSide)))
	case <-cancelTimeout.C:
		s.logger.Warn("Cancellation timed out during limit enforcement",
			zap.String("order_id", cancelledOrder.View().InternalID))
		return
	case <-s.mainCtx.Done():
		s.logger.Debug("Cancellation cancelled due to shutdown during limit enforcement",
			zap.String("order_id", cancelledOrder.View().InternalID))
		return
	}

	// Step 2: Now that cancellation is confirmed, create the new order
	params := ordermanager.SendOrderParams{
		Market: s.market,
		Tag:    fmt.Sprintf("mkt-maker-replace-%s-%d", newOrderSide, rand.Intn(1000)),
		Price:  68500.0 + (rand.Float64() * 100) - 50,
		Amount: 0.01 + rand.Float64()*0.09,
		Side:   newOrderSide,
	}

	// Create order immediately (don't launch another goroutine)
	order, err := s.mom.SendOrder(s.mainCtx, params)
	if err != nil {
		s.logger.Warn("Failed to create replacement order after cancellation",
			zap.String("cancelled_order", cancelledOrderView.InternalID),
			zap.Error(err))
		return
	}

	newOrderView := order.View()
	s.logger.Info("Replacement order created after limit enforcement",
		zap.String("cancelled_order", cancelledOrderView.InternalID),
		zap.String("new_order", newOrderView.InternalID),
		zap.String("side", string(newOrderView.Side)))

	// Wait for confirmation of new order with timeout
	newOrderTimeout := time.NewTimer(5 * time.Second)
	defer newOrderTimeout.Stop()

	select {
	case err := <-order.Confirmed():
		if err != nil {
			s.logger.Warn("Replacement order confirmation failed",
				zap.String("order_id", newOrderView.InternalID),
				zap.Error(err))
		} else {
			confirmedView := order.View() // Get updated view after confirmation
			s.logger.Info("Replacement order confirmed - limit enforcement complete",
				zap.String("order_id", confirmedView.InternalID),
				zap.String("exchange_id", confirmedView.OrderID))
		}
	case <-newOrderTimeout.C:
		s.logger.Warn("Replacement order confirmation timed out",
			zap.String("order_id", newOrderView.InternalID))
	case <-s.mainCtx.Done():
		s.logger.Debug("Replacement order confirmation cancelled due to shutdown",
			zap.String("order_id", newOrderView.InternalID))
	}
}

// getCancellingOrdersCount returns the number of currently cancelling orders
func (s *simulator) getCancellingOrdersCount() int {
	count := 0
	s.cancellingOrders.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}

// cancelRandomOrders cancels a random percentage of the given orders
func (s *simulator) cancelRandomOrders(orders []*ordermanager.ManagedOrder, percentage float64) {
	if len(orders) == 0 || percentage <= 0 {
		return
	}

	// Calculate how many orders to cancel
	numToCancel := int(float64(len(orders)) * percentage)
	if numToCancel == 0 {
		numToCancel = 1 // Cancel at least one if we have orders
	}

	s.logger.Info("Cancelling random orders",
		zap.Int("total_orders", len(orders)),
		zap.Float64("percentage", percentage),
		zap.Int("num_to_cancel", numToCancel))

	// Shuffle the orders slice and take the first numToCancel
	shuffled := make([]*ordermanager.ManagedOrder, len(orders))
	copy(shuffled, orders)

	// Fisher-Yates shuffle
	for i := len(shuffled) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	}

	// Cancel the first numToCancel orders from the shuffled list
	for i := 0; i < numToCancel && i < len(shuffled); i++ {
		go s.cancelOrderAsync(shuffled[i])
	}
}
