package marketdata

import (
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
	"github.com/herenow/atomic-tools/pkg/ratetracker"
	"go.uber.org/zap"
)

// MarketState tracks core properties of a market in a thread-safe manner
type MarketState struct {
	mu              sync.RWMutex
	sequence        int64
	lastUpdate      time.Time
	lastCheck       time.Time
	lastStateChange time.Time
	lastError       error
	suspendedUntil  time.Time
	state           MarketStateEnum
	book            *book.Book
	updatePerMinute *ratetracker.Rate
	failedAttempts  uint32
	logger          *zap.Logger
	onStateChange   onStateChangeFunc

	metrics        IMarketStateMetrics
	market         gateway.Market
	stateStartTime time.Time
}

// Note: this callback is called while m.state is locked, so it is safe to access m.state
// but not safe to access any getters or setters on m.state.
type onStateChangeFunc func(sequence int64, oldState, newState MarketStateEnum, err error)

func NewMarketState(market gateway.Market, logger *zap.Logger, metrics IMarketStateMetrics) *MarketState {
	if metrics == nil {
		metrics = NewNoopMarketMetrics()
	}

	log := logger.With(
		zap.String("exchange", market.Exchange.Name),
		zap.String("market", market.Symbol),
	)

	return &MarketState{
		state:           MarketStateQueued,
		market:          market,
		book:            book.NewBook(market.String()),
		updatePerMinute: ratetracker.New(60),
		logger:          log,
		metrics:         metrics,
	}
}

// Thread-safe getters

func (m *MarketState) Sequence() int64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.sequence
}

func (m *MarketState) LastUpdate() time.Time {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastUpdate
}

func (m *MarketState) LastCheck() time.Time {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastCheck
}

func (m *MarketState) LastStateChange() time.Time {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastStateChange
}

func (m *MarketState) LastError() error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.lastError
}

func (m *MarketState) State() MarketStateEnum {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.state
}

func (m *MarketState) Book() *book.Book {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.book
}

func (m *MarketState) UpdatePerMinute() *ratetracker.Rate {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.updatePerMinute
}

// Thread-safe setters

func (m *MarketState) SetSequence(seq int64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.sequence = seq
}

func (m *MarketState) SetLastUpdate(t time.Time) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.lastUpdate = t
}

func (m *MarketState) SetLastCheck(t time.Time) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.lastCheck = t
}

// State management methods

func (m *MarketState) SetState(newState MarketStateEnum, err error) {
	m.mu.Lock()
	m.setState(newState, err)
	m.mu.Unlock()

	m.metrics.TrackMarketState(m)
}

func (m *MarketState) setState(newState MarketStateEnum, err error) {
	oldState := m.state

	// Check if transition is valid
	if !IsValidTransition(oldState, newState) {
		m.logger.Error("invalid market state transition",
			zap.String("from_state", oldState.String()),
			zap.String("to_state", newState.String()))
		return
	}

	// Update state
	m.state = newState
	m.lastStateChange = time.Now()
	m.lastError = err
	m.sequence++

	// Log transition
	if err != nil {
		m.logger.Debug("market state changed",
			zap.String("from_state", oldState.String()),
			zap.String("to_state", newState.String()),
			zap.Error(err))
	} else {
		m.logger.Debug("market state changed",
			zap.String("from_state", oldState.String()),
			zap.String("to_state", newState.String()))
	}

	// Call state change callback
	if m.onStateChange != nil {
		m.onStateChange(m.sequence, oldState, newState, err)
	}
}

func (m *MarketState) RecordUpdateReceived(t time.Time) int64 {
	m.mu.Lock()
	m.lastUpdate = t
	m.sequence++
	m.updatePerMinute.Increment()
	m.mu.Unlock()

	return m.Sequence()
}

// Suspension methods

func (m *MarketState) IsSuspended() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return time.Now().Before(m.suspendedUntil)
}

func (m *MarketState) SuspendUntil(t time.Time) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.suspendedUntil = t
}

func (m *MarketState) suspendUntil(t time.Time) {
	m.suspendedUntil = t
}

func (m *MarketState) SuspendFor(d time.Duration) {
	m.SuspendUntil(time.Now().Add(d))
}

func (m *MarketState) suspendFor(d time.Duration) {
	m.suspendUntil(time.Now().Add(d))
}

// State check methods

func (m *MarketState) IsInitializing() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.state == MarketStateInitializing
}

func (m *MarketState) IsQueued() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.state == MarketStateQueued
}

func (m *MarketState) IsReady() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.state != MarketStateReady {
		return false
	}

	if time.Now().Before(m.suspendedUntil) {
		return false
	}

	return true
}

// Failure handling methods

func (m *MarketState) CanReconnect() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Cannot reconnect stopped markets
	if m.state == MarketStateStopped {
		return false
	}

	// Can only reconnect if in terminal state
	if isTerminalState(m.state) {
		// Check if not suspended
		if !m.IsSuspended() {
			return true
		}
	}

	return false
}

func (m *MarketState) FailedAttempts() uint32 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.failedAttempts
}

func (m *MarketState) SetFailed(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.metrics.TrackError(m, "failure")

	m.setState(MarketStateFailed, err)

	// Suspend for a backoff duration
	m.failedAttempts++
	backoff := getBackoffDuration(m.failedAttempts)
	m.suspendFor(backoff)
}

func (m *MarketState) SetDisconnected(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.metrics.TrackError(m, "disconnected")

	m.setState(MarketStateDisconnected, err)

	// Suspend for a backoff duration
	m.failedAttempts++
	backoff := getBackoffDuration(m.failedAttempts)
	m.suspendFor(backoff)
}

func (m *MarketState) ResetFailure() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.failedAttempts = 0
}

func (m *MarketState) SetOutOfSync(err error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Can only set Ready state to OutOfSync
	if m.state != MarketStateReady {
		m.logger.Error("cannot set market out of sync",
			zap.String("from_state", m.state.String()),
			zap.Error(err))
		return
	}

	m.metrics.TrackError(m, "out_of_sync")

	m.setState(MarketStateOutOfSync, err)
}

func (m *MarketState) SetOnStateChange(f onStateChangeFunc) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.onStateChange = f
}

// Internal helper methods

func getBackoffDuration(failedAttempts uint32) time.Duration {
	// Start with 5s, increment by 10s each time, cap at 60s
	backoff := 5*time.Second + (10 * time.Second * time.Duration(failedAttempts-1))
	if backoff > 60*time.Second {
		backoff = 60 * time.Second
	}
	return backoff
}
