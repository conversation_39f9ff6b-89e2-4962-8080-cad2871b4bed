package marketdata

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
	"go.uber.org/zap"
)

type MarketUpdate struct {
	Sequence  int64
	Timestamp time.Time

	// Change data
	Bids       []book.Price
	Asks       []book.Price
	IsSnapshot bool
	Trades     []gateway.Trade
	NewState   MarketStateEnum
}

type MarketManager struct {
	mu     sync.RWMutex
	logger *zap.Logger
	state  *MarketState

	waitingInitialSnapshot bool

	updateCh chan MarketUpdate
	stopCh   chan struct{}

	// Subscriber management
	subscribers map[chan<- MarketUpdate]struct{}
	subMu       sync.RWMutex

	// Associated gtw instance
	gtwInstance *GatewayInstance
}

func NewMarketManager(market gateway.Market, logger *zap.Logger, metrics IMarketStateMetrics) *MarketManager {
	mgr := &MarketManager{
		logger:      logger,
		state:       NewMarketState(market, logger, metrics),
		updateCh:    make(chan <PERSON>date, 1000),
		stopCh:      make(chan struct{}),
		subscribers: make(map[chan<- MarketUpdate]struct{}),
	}

	mgr.state.onStateChange = mgr.onStateChange

	return mgr
}

// AssociateGatewayInstance associates a gateway instance with this market manager
// If the gateway instance is already associated, this will return false
func (m *MarketManager) AssociateGatewayInstance(gtwInstance *GatewayInstance) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.gtwInstance != nil {
		return fmt.Errorf("gateway instance already associated with market manager")
	}

	m.gtwInstance = gtwInstance
	return nil
}

func (m *MarketManager) ReleaseGatewayInstance(gtwInstance *GatewayInstance) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Check if this gateway instance is associated with this market manager
	if m.gtwInstance != gtwInstance {
		return fmt.Errorf("gateway instance not associated with this market manager")
	}

	m.gtwInstance = nil
	return nil
}

// Subscribe returns a channel that will receive market updates
// The channel will be closed when:
// - The market is stopped/unsubscribed
// - The context is cancelled
// - The subscriber is removed
func (m *MarketManager) Subscribe(ctx context.Context) <-chan MarketUpdate {
	ch := make(chan MarketUpdate, 1000)

	m.subMu.Lock()
	m.subscribers[ch] = struct{}{}
	m.subMu.Unlock()

	// Clean up subscription when context is cancelled
	go func() {
		<-ctx.Done()
		m.subMu.Lock()
		delete(m.subscribers, ch)
		m.subMu.Unlock()
		close(ch)
	}()

	return ch
}

// ProcessUpdate processes a market data update
func (m *MarketManager) ProcessUpdate(update MarketUpdate) {
	// Update internal state, this will also update the sequence number
	update.Sequence = m.state.RecordUpdateReceived(update.Timestamp)

	// Update book
	if update.IsSnapshot {
		m.state.Book().Snapshot(update.Bids, update.Asks)
	} else {
		m.state.Book().DeltaUpdate(update.Bids, update.Asks)
	}

	// Set state to ready if initial snapshot received
	m.mu.Lock()
	waitingInitialSnapshot := m.waitingInitialSnapshot
	m.mu.Unlock()
	if waitingInitialSnapshot && (len(update.Bids) > 0 || len(update.Asks) > 0) {
		m.state.SetState(MarketStateReady, nil)
	}

	// Notify subscribers
	m.subMu.RLock()
	for ch := range m.subscribers {
		ch <- update
	}
	m.subMu.RUnlock()
}

// Update state and notify subscribers
// Note: this callback is called while m.state is locked, so it is safe to access m.state
// but not safe to access any getters or setters on m.state.
func (m *MarketManager) onStateChange(sequence int64, _, newState MarketStateEnum, err error) {
	update := MarketUpdate{
		Sequence:  sequence,
		Timestamp: time.Now(),
		NewState:  newState,
	}

	// Notify subscribers
	m.subMu.RLock()
	for ch := range m.subscribers {
		ch <- update
	}
	m.subMu.RUnlock()
}

// GetState returns a copy of the current market state
func (m *MarketManager) GetState() *MarketState {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.state
}
