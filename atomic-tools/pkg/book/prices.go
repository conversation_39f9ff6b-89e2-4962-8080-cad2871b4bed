package book

import (
	"fmt"
	"log"
	"sort"
	"sync"
	"time"
)

type NotEnoughLiquidityErr struct {
	Book *Book
}

// Return error message, this is part of the error interface.
func (e *NotEnoughLiquidityErr) Error() string {
	return fmt.Sprintf("There was not enough liquidity on order book %s", e.Book.Symbol())
}

type Prices struct {
	side         BidAskSide
	maxDepth     int
	data         map[float64]Price
	levels       []float64
	mutex        *sync.Mutex
	book         *Book
	eventHistory *EventHistory
}

func NewPrices(book *Book, side BidAskSide, eh *EventHistory) *Prices {
	return &Prices{
		book:         book,
		side:         side,
		maxDepth:     5000,
		data:         make(map[float64]Price),
		levels:       make([]float64, 0),
		mutex:        &sync.Mutex{},
		eventHistory: eh,
	}
}

func (prices *Prices) Length() int {
	return len(prices.levels)
}

func (prices *Prices) Reset() {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	prices.data = make(map[float64]Price)
	prices.levels = make([]float64, 0)
}

func (prices *Prices) Update(pxs []Price) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	news := make([]Price, 0)
	chgs := make([]Price, 0)
	dels := make([]int, 0)

	for _, px := range pxs {
		idx, ok := prices.priceIndex(px.Value)

		if ok {
			if px.Amount > 0 {
				existingPrice := prices.data[px.Value]
				existingPrice.Amount = px.Amount // Update the amount of the existing price
				chgs = append(chgs, existingPrice)
			} else {
				if intsContains(dels, idx) {
					log.Printf(
						"There is something wrong with the market data! "+
							"Received del twice for price [%f] on side [%s] "+
							"on book [%s] at index [%d].",
						px.Value, prices.side, prices.book.Symbol(), idx,
					)
				} else {
					dels = append(dels, idx)
				}
			}
		} else {
			if px.Amount > 0 {
				px.EntryTime = time.Now() // Register first time we see this price
				news = append(news, px)
			} else {
				// Ignore, we don't event have this price
				// registered, we don't need to delete it
			}
		}
	}

	// We need to sort our dels, we need to delete the
	// lowest index first, and move from there.
	sort.Ints(dels)

	for i, idx := range dels {
		// Each time we remove an index, the price index
		// array gets, smaller, so we need to account for
		// that by removing N from the "removal index"
		prices.removePriceIndex(idx - i)
	}
	for _, px := range chgs {
		prices.updatePrice(px)
	}
	for _, px := range news {
		prices.addPrice(px)
	}

	if len(news) > 0 {
		prices.sortPrices()
	}
}

func (prices *Prices) PriceLevels() []Price {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	priceLevels := make([]Price, len(prices.levels))

	for index, price := range prices.levels {
		priceLevels[index] = prices.data[price]
	}

	return priceLevels
}

func (prices *Prices) PriceIndex(price float64) (int, bool) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	return prices.priceIndex(price)
}

func (prices *Prices) PriceData(price float64) (Price, bool) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()
	if px, ok := prices.data[price]; ok {
		return px, true
	}
	return Price{}, false
}

func (prices *Prices) priceIndex(price float64) (int, bool) {
	for index, value := range prices.levels {
		if price == value {
			return index, true
		}
	}

	return -1, false
}

func (prices *Prices) RemovePriceIndex(index int) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	prices.removePriceIndex(index)
}

func (prices *Prices) removePriceIndex(index int) {
	px := prices.levels[index]

	prices.eventHistory.AddEvent(EventRemove, prices.side, prices.data[px])

	delete(prices.data, px)
	prices.levels = append(prices.levels[:index], prices.levels[index+1:]...)
}

func (prices *Prices) addPrice(px Price) {
	prices.levels = append(prices.levels, px.Value)
	prices.data[px.Value] = px

	prices.sortPrices()

	// If we exceed maxDepth, remove the worst price(s)
	if len(prices.levels) > prices.maxDepth {
		excess := len(prices.levels) - prices.maxDepth
		for i := 0; i < excess; i++ {
			lastIdx := len(prices.levels) - 1
			lastPrice := prices.levels[lastIdx]
			delete(prices.data, lastPrice)
			prices.levels = prices.levels[:lastIdx]
		}
	}
}

func (prices *Prices) updatePrice(px Price) {
	prices.data[px.Value] = px
}

func (prices *Prices) sortPrices() {
	if prices.side == Bid {
		sort.Sort(sort.Reverse(sort.Float64Slice(prices.levels)))
	} else {
		sort.Sort(sort.Float64Slice(prices.levels))
	}
}

func (prices *Prices) Top() (Price, bool) {
	return prices.TopN(0)
}

func (prices *Prices) TopN(n int) (Price, bool) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	if len(prices.levels) <= n {
		return Price{}, false
	}

	price := prices.levels[n]

	return prices.data[price], true
}

func (prices *Prices) TopRange(n int) []Price {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	if n > len(prices.levels) {
		n = len(prices.levels)
	}

	levels := prices.levels[:n]
	data := make([]Price, n)
	for i, p := range levels {
		data[i] = prices.data[p]
	}

	return data
}

func (prices *Prices) UntilPrice(price float64) []Price {
	side := prices.side
	_range := 0

	prices.mutex.Lock()
	for index, value := range prices.levels {
		if side == Bid {
			if value >= price {
				_range = index + 1
			} else {
				break
			}
		} else if side == Ask {
			if value <= price {
				_range = index + 1
			} else {
				break
			}
		}
	}
	prices.mutex.Unlock()

	if _range > 0 {
		return prices.TopRange(_range)
	}

	return []Price{}
}

func (prices *Prices) Bottom() (Price, bool) {
	prices.mutex.Lock()
	defer prices.mutex.Unlock()

	if len(prices.levels) == 0 {
		return Price{}, false
	}

	lastPrice := prices.levels[len(prices.levels)-1]
	return prices.data[lastPrice], true
}

func intsContains(ints []int, i int) bool {
	for _, v := range ints {
		if v == i {
			return true
		}
	}
	return false
}
