package book

import (
	"math/rand"
	"sync"
	"testing"
	"time"
)

func TestPriceTimestampPreservation(t *testing.T) {
	b := NewBook("TEST/USD")

	initialBids := []Price{
		{Value: 100.0, Amount: 1.0},
		{Value: 99.0, Amount: 2.0},
	}
	initialAsks := []Price{
		{Value: 101.0, Amount: 1.0},
		{Value: 102.0, Amount: 2.0},
	}
	b.Snapshot(initialBids, initialAsks)

	initialTime := time.Now()
	time.Sleep(10 * time.Millisecond)

	deltaUpdate := []Price{
		{Value: 98.0, Amount: 3.0}, // New price
	}
	b.DeltaUpdate(deltaUpdate, nil)

	time.Sleep(10 * time.Millisecond)

	newBids := []Price{
		{Value: 100.0, Amount: 1.5}, // Existing price with updated amount
		{Value: 98.0, Amount: 3.5},  // Price added in delta
		{Value: 97.0, Amount: 4.0},  // New price
	}
	newAsks := []Price{
		{Value: 101.0, Amount: 1.5}, // Existing price with updated amount
		{Value: 103.0, Amount: 3.0}, // New price
	}
	b.Snapshot(newBids, newAsks)

	// 1. Price that existed since the beginning should have an old timestamp
	age100, ok := b.GetPriceAge(100.0, Bid)
	if !ok {
		t.Errorf("Price 100.0 not found")
	}
	if age100 < time.Since(initialTime) {
		t.Errorf("Timestamp of price 100.0 was reset")
	}

	// 2. Price added in delta should have intermediate timestamp
	age98, ok := b.GetPriceAge(98.0, Bid)
	if !ok {
		t.Errorf("Price 98.0 not found")
	}
	if age98 > time.Since(initialTime) || age98 < 10*time.Millisecond {
		t.Errorf("Timestamp of price 98.0 incorrect")
	}

	// 3. Price added in last snapshot should have recent timestamp
	age97, ok := b.GetPriceAge(97.0, Bid)
	if !ok {
		t.Errorf("Price 97.0 not found")
	}
	if age97 > 20*time.Millisecond {
		t.Errorf("Timestamp of price 97.0 too old: %v", age97)
	}

	// 4. Price removed should not exist
	_, ok = b.GetPriceAge(99.0, Bid)
	if ok {
		t.Errorf("Price 99.0 still exists, but should have been removed")
	}
}

func TestConcurrentTimestampAccess(t *testing.T) {
	b := NewBook("TEST/USD")

	initialBids := []Price{
		{Value: 100.0, Amount: 1.0},
	}
	b.Snapshot(initialBids, nil)

	var wg sync.WaitGroup
	for i := range 100 {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()

			switch i % 3 {
			case 0:
				b.DeltaUpdate([]Price{{Value: 100.0, Amount: float64(i) * 0.1}}, nil)
			case 1:
				b.GetPriceAge(100.0, Bid)
			default:
				b.GetPriceEntryTime(100.0, Bid)
			}
		}(i)
	}

	wg.Wait()
	// If no deadlock or panic, the test passes
}

func TestTimestampPreservationDuringSnapshot(t *testing.T) {
	// Arrange
	book := NewBook("BTC/USD")
	initialBids := []Price{
		{Value: 10000.0, Amount: 1.0},
		{Value: 9900.0, Amount: 2.0},
	}
	book.Snapshot(initialBids, nil)

	creationTime := time.Now()
	time.Sleep(50 * time.Millisecond)

	// Act: Apply new snapshot with the same price but different quantity
	updatedBids := []Price{
		{Value: 10000.0, Amount: 1.5}, // Same price, different quantity
		{Value: 9800.0, Amount: 3.0},  // New price
	}
	book.Snapshot(updatedBids, nil)

	// Assert
	originalPriceAge, exists := book.GetPriceAge(10000.0, Bid)
	if !exists {
		t.Fatal("Original price should exist after snapshot")
	}

	// The original price should have age close to time since creation
	timeSinceCreation := time.Since(creationTime)
	if originalPriceAge < timeSinceCreation-10*time.Millisecond {
		t.Errorf("Timestamp of original price was reset: age=%v, expected ~%v",
			originalPriceAge, timeSinceCreation)
	}

	// The new price should have recent timestamp
	newPriceAge, exists := book.GetPriceAge(9800.0, Bid)
	if !exists {
		t.Fatal("New price should exist after snapshot")
	}
	if newPriceAge > 10*time.Millisecond {
		t.Errorf("New price has too old timestamp: %v", newPriceAge)
	}

	// Removed price should not exist anymore
	_, exists = book.GetPriceAge(9900.0, Bid)
	if exists {
		t.Error("Removed price still exists after snapshot")
	}
}

func TestTimestampPreservationDuringDeltaUpdates(t *testing.T) {
	// Arrange
	book := NewBook("ETH/USD")
	initialBids := []Price{
		{Value: 2000.0, Amount: 1.0},
	}
	book.Snapshot(initialBids, nil)

	creationTime := time.Now()
	time.Sleep(50 * time.Millisecond)

	// Act: Update same price via delta update
	book.DeltaUpdate([]Price{{Value: 2000.0, Amount: 1.5}}, nil)

	// Assert
	priceAge, exists := book.GetPriceAge(2000.0, Bid)
	if !exists {
		t.Fatal("Price should exist after delta update")
	}

	// The timestamp should be preserved even after delta update
	timeSinceCreation := time.Since(creationTime)
	if priceAge < timeSinceCreation-10*time.Millisecond {
		t.Errorf("Timestamp was reset after delta update: age=%v, expected ~%v",
			priceAge, timeSinceCreation)
	}
}

func TestTimestampAccuracyForNewPrices(t *testing.T) {
	// Arrange
	book := NewBook("XRP/USD")

	// Act: Add a new price and measure the time
	startTime := time.Now()
	book.DeltaUpdate([]Price{{Value: 0.5, Amount: 1000.0}}, nil)
	endTime := time.Now()

	// Assert
	priceAge, exists := book.GetPriceAge(0.5, Bid)
	if !exists {
		t.Fatal("Recently added price not found")
	}

	// The price age should be between 0 and the elapsed time during the operation
	maxExpectedAge := time.Since(startTime)
	minExpectedAge := time.Since(endTime)

	if priceAge > maxExpectedAge || priceAge < minExpectedAge {
		t.Errorf("Price age outside expected range: %v, expected between %v and %v",
			priceAge, minExpectedAge, maxExpectedAge)
	}
}

func TestTimestampConsistencyUnderLoad(t *testing.T) {
	// Arrange
	book := NewBook("BTC/USD")
	initialPrice := 50000.0
	book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0}}, nil)

	creationTime := time.Now()
	time.Sleep(50 * time.Millisecond)

	// Act: Realize multiple delta updates in quick succession
	const updateCount = 100
	for i := range updateCount {
		// Keep the same price, change only the amount
		newAmount := 1.0 + float64(i)*0.01
		book.DeltaUpdate([]Price{{Value: initialPrice, Amount: newAmount}}, nil)
	}

	// Assert
	priceAge, exists := book.GetPriceAge(initialPrice, Bid)
	if !exists {
		t.Fatal("Price should exist after multiple updates")
	}

	// The timestamp should be preserved even after multiple updates
	timeSinceCreation := time.Since(creationTime)
	if priceAge < timeSinceCreation-10*time.Millisecond {
		t.Errorf("Timestamp was reset after multiple updates: age=%v, expected ~%v",
			priceAge, timeSinceCreation)
	}
}

func TestTimestampBehaviorWithZeroAmount(t *testing.T) {
	// Arrange
	book := NewBook("ETH/USD")
	price := 3000.0

	// Add a price
	book.DeltaUpdate([]Price{{Value: price, Amount: 1.0}}, nil)

	time.Sleep(50 * time.Millisecond)

	// Act: Remove the price (amount = 0)
	book.DeltaUpdate([]Price{{Value: price, Amount: 0.0}}, nil)

	// Assert: The price should not exist anymore
	_, exists := book.GetPriceAge(price, Bid)
	if exists {
		t.Error("Price with amount=0 still exists in book")
	}

	// Act: Add the same price again
	book.DeltaUpdate([]Price{{Value: price, Amount: 2.0}}, nil)

	// Assert: Should have a new timestamp
	priceAge, exists := book.GetPriceAge(price, Bid)
	if !exists {
		t.Fatal("Recently added price not found")
	}

	// The timestamp should be recent, not preserved from the previous price
	if priceAge > 10*time.Millisecond {
		t.Errorf("Recently added price has old timestamp: %v", priceAge)
	}
}

func TestGetPriceEntryTimeConsistency(t *testing.T) {
	// Arrange
	book := NewBook("LTC/USD")
	price := 100.0
	book.DeltaUpdate([]Price{{Value: price, Amount: 1.0}}, nil)

	// Act: Get both age and entry time
	age, ageExists := book.GetPriceAge(price, Bid)
	entryTime, timeExists := book.GetPriceEntryTime(price, Bid)

	// Assert
	if !ageExists || !timeExists {
		t.Fatal("Failed to retrieve age or entry time")
	}

	// The age calculated from the entry time should be approximately equal to the returned age
	calculatedAge := time.Since(entryTime)
	ageDifference := calculatedAge - age
	if ageDifference < 0 {
		ageDifference = -ageDifference
	}

	// Allow a small difference due to the execution time between calls
	if ageDifference > 5*time.Millisecond {
		t.Errorf("Inconsistency between GetPriceAge and GetPriceEntryTime: |%v - %v| = %v",
			calculatedAge, age, ageDifference)
	}
}

func TestConcurrentTimestampOperations(t *testing.T) {
	// Arrange
	book := NewBook("BTC/USD")
	initialPrice := 50000.0
	book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0}}, nil)

	creationTime := time.Now()
	time.Sleep(50 * time.Millisecond)

	// Act: Execute concurrent operations
	const goroutineCount = 100
	var wg sync.WaitGroup
	wg.Add(goroutineCount)

	for i := range goroutineCount {
		go func(index int) {
			defer wg.Done()

			// Different operations based on index
			switch index % 4 {
			case 0:
				book.DeltaUpdate([]Price{{Value: initialPrice, Amount: 1.0 + float64(index)*0.01}}, nil)
			case 1:
				book.GetPriceAge(initialPrice, Bid)
			case 2:
				book.GetPriceEntryTime(initialPrice, Bid)
			case 3:
				book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0 + float64(index)*0.01}}, nil)
			}
		}(i)
	}

	wg.Wait()

	// Assert: The original price should have age close to time since creation
	priceAge, exists := book.GetPriceAge(initialPrice, Bid)
	if !exists {
		t.Fatal("Price should exist after concurrent operations")
	}

	timeSinceCreation := time.Since(creationTime)
	if priceAge < timeSinceCreation-10*time.Millisecond {
		t.Errorf("Timestamp was reset after concurrent operations: age=%v, expected ~%v",
			priceAge, timeSinceCreation)
	}
}

func TestTimestampBehaviorWithManyPriceLevels(t *testing.T) {
	// Arrange
	book := NewBook("BTC/USD")
	const priceCount = 250

	bids := make([]Price, priceCount)
	for i := range bids {
		bids[i] = Price{
			Value:  10000.0 - float64(i),
			Amount: 1.0,
		}
	}

	// Act: Apply snapshot with many prices
	book.Snapshot(bids, nil)
	time.Sleep(50 * time.Millisecond)

	for range 10 {
		randomIndex := rand.Intn(priceCount)
		price := 10000.0 - float64(randomIndex)

		// Assert
		age, exists := book.GetPriceAge(price, Bid)
		if !exists {
			t.Errorf("Price %.2f not found", price)
			continue
		}

		if age < 40*time.Millisecond {
			t.Errorf("Incorrect timestamp for price %.2f: %v", price, age)
		}
	}
}

func TestTimestampBehaviorWithFrequentSnapshots(t *testing.T) {
	// Arrange
	book := NewBook("ETH/USD")
	initialPrice := 2000.0
	book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0}}, nil)

	creationTime := time.Now()
	time.Sleep(50 * time.Millisecond)

	// Act: Apply frequent snapshots
	const snapshotCount = 50
	for i := range snapshotCount {
		book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0 + float64(i)*0.01}}, nil)

		tempPrice := initialPrice + float64(i+1)
		book.DeltaUpdate([]Price{{Value: tempPrice, Amount: 0.1}}, nil)
	}

	book.Snapshot([]Price{{Value: initialPrice, Amount: 1.0}}, nil)

	// Assert: The timestamp of the original price should be preserved
	priceAge, exists := book.GetPriceAge(initialPrice, Bid)
	if !exists {
		t.Fatal("Original price should exist after frequent snapshots")
	}

	timeSinceCreation := time.Since(creationTime)
	if priceAge < timeSinceCreation-10*time.Millisecond {
		t.Errorf("Timestamp was reset after frequent snapshots: age=%v, expected ~%v",
			priceAge, timeSinceCreation)
	}

	// The temporary prices should not exist anymore
	for i := range snapshotCount {
		tempPrice := initialPrice + float64(i+1)
		_, exists := book.GetPriceAge(tempPrice, Bid)
		if exists {
			t.Errorf("Temporary price %.2f still exists after being removed by snapshot", tempPrice)
		}
	}
}

func TestEntryTimeAge(t *testing.T) {
	// Test zero time returns zero duration
	p1 := Price{
		Value:     100.0,
		Amount:    1.0,
		EntryTime: time.Time{},
	}
	if age := p1.Age(); age != 0 {
		t.Errorf("Zero EntryTime should return zero age, got %v", age)
	}

	// Test non-zero time returns positive duration
	entryTime := time.Now()
	p2 := Price{
		Value:     100.0,
		Amount:    1.0,
		EntryTime: entryTime,
	}

	time.Sleep(50 * time.Millisecond)

	age := p2.Age()
	expectedAge := time.Since(entryTime)

	// Allow small difference due to execution time
	diff := expectedAge - age
	if diff < 0 {
		diff = -diff
	}

	if diff > 5*time.Millisecond {
		t.Errorf("Age calculation incorrect: got %v, expected ~%v", age, expectedAge)
	}
}
